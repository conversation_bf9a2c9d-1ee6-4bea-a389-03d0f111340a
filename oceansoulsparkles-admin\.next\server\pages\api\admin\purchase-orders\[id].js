"use strict";(()=>{var e={};e.id=4434,e.ids=[4434],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},830:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>p,routeModule:()=>f});var s={};t.r(s),t.d(s,{default:()=>u});var o=t(1802),a=t(7153),n=t(8781),d=t(7474);async function u(e,r){let t=`purchase-order-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:s}=e.query;if(console.log(`[${t}] Individual purchase order API request:`,{method:e.method,purchaseOrderId:s,userAgent:e.headers["user-agent"]}),!s||"string"!=typeof s)return r.status(400).json({error:"Invalid purchase order ID",requestId:t});let o=await (0,d.Wg)(e);if(!o.valid)return console.log(`[${t}] Authentication failed:`,o.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(o.user.role))return console.log(`[${t}] Insufficient permissions:`,o.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await i(e,r,t,s);if("PUT"===e.method)return await c(e,r,t,s,o.user);if("DELETE"===e.method)return await l(e,r,t,s,o.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Individual purchase order API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function i(e,r,t,s){try{let{data:e,error:o}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone,
          address,
          payment_terms,
          lead_time_days
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `).eq("id",s).single();if(o){if("PGRST116"===o.code)return r.status(404).json({error:"Purchase order not found",requestId:t});throw o}let{data:a,error:n}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_order_items").select(`
        *,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `).eq("purchase_order_id",s).order("created_at");return n&&console.warn(`[${t}] Error fetching purchase order items:`,n),console.log(`[${t}] Purchase order fetched successfully:`,{id:e.id,poNumber:e.po_number,supplier:e.suppliers?.name,itemCount:a?.length||0}),r.status(200).json({purchaseOrder:{...e,items:a||[]},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching purchase order:`,e),e}}async function c(e,r,t,s,o){try{let{status:a,expectedDeliveryDate:n,actualDeliveryDate:d,notes:u}=e.body,{data:i,error:c}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select("id, po_number, status, supplier_id").eq("id",s).single();if(c){if("PGRST116"===c.code)return r.status(404).json({error:"Purchase order not found",requestId:t});throw c}let l=["draft","sent","confirmed","received","cancelled"];if(a&&!l.includes(a))return r.status(400).json({error:"Invalid status",message:`Status must be one of: ${l.join(", ")}`,requestId:t});if(["received","cancelled"].includes(i.status)&&a!==i.status)return r.status(400).json({error:"Cannot modify completed purchase order",message:"Purchase orders that are received or cancelled cannot be modified",requestId:t});let p={updated_at:new Date().toISOString()};a&&(p.status=a),n&&(p.expected_delivery_date=n),d&&(p.actual_delivery_date=d),void 0!==u&&(p.notes=u?.trim()||null);let{data:m,error:f}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").update(p).eq("id",s).select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email
        )
      `).single();if(f)throw console.error(`[${t}] Database error updating purchase order:`,f),f;return console.log(`[${t}] Purchase order updated successfully:`,{id:m.id,poNumber:m.po_number,status:m.status,updatedBy:o.email}),r.status(200).json({purchaseOrder:m,message:"Purchase order updated successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error updating purchase order:`,e),e}}async function l(e,r,t,s,o){try{let{data:e,error:a}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select("id, po_number, status").eq("id",s).single();if(a){if("PGRST116"===a.code)return r.status(404).json({error:"Purchase order not found",requestId:t});throw a}if("draft"!==e.status)return r.status(400).json({error:"Cannot delete purchase order",message:"Only draft purchase orders can be deleted",requestId:t});let{error:n}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").delete().eq("id",s);if(n)throw console.error(`[${t}] Database error deleting purchase order:`,n),n;return console.log(`[${t}] Purchase order deleted successfully:`,{id:s,poNumber:e.po_number,deletedBy:o.email}),r.status(200).json({message:"Purchase order deleted successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error deleting purchase order:`,e),e}}!function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}();let p=(0,n.l)(s,"default"),m=(0,n.l)(s,"config"),f=new o.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/purchase-orders/[id]",pathname:"/api/admin/purchase-orders/[id]",bundlePath:"",filename:""},userland:s})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(830));module.exports=s})();