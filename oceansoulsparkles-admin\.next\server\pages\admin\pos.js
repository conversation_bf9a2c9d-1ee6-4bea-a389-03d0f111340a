(()=>{var e={};e.id=2451,e.ids=[2451,660],e.modules={1073:e=>{e.exports={posContainer:"POS_posContainer__6tGk7",header:"POS_header__NK5rM",title:"POS_title__aubSl",headerActions:"POS_headerActions__qWBFv",backButton:"POS_backButton__LBI7I",posLayout:"POS_posLayout__E66LX",servicesPanel:"POS_servicesPanel__5pgzh",serviceCategories:"POS_serviceCategories__GWW0s",categorySection:"POS_categorySection__VeYj5",categoryTitle:"POS_categoryTitle__4bPnN",serviceGrid:"POS_serviceGrid__ebdfY",serviceCard:"POS_serviceCard__895l8",serviceName:"POS_serviceName__HDmnI",servicePrice:"POS_servicePrice__8UUJz",serviceDuration:"POS_serviceDuration__9hI3C",cartPanel:"POS_cartPanel__6lBFL",customerSection:"POS_customerSection__59W3o",customerInfo:"POS_customerInfo__B3ZRB",changeCustomerBtn:"POS_changeCustomerBtn__E2veb",customerActions:"POS_customerActions__GZ3NQ",addCustomerBtn:"POS_addCustomerBtn__wIE0A",walkInBtn:"POS_walkInBtn__vNBwC",cartItems:"POS_cartItems__fuaCS",emptyCart:"POS_emptyCart__LyoKT",cartItem:"POS_cartItem__l0Fl8",itemInfo:"POS_itemInfo__qS4zm",itemName:"POS_itemName__rLI5l",itemPrice:"POS_itemPrice__iy5yI",itemControls:"POS_itemControls__EQzIb",quantityBtn:"POS_quantityBtn__MK9lD",quantity:"POS_quantity__tdGdn",removeBtn:"POS_removeBtn__AD3a2",paymentSection:"POS_paymentSection__j0cHT",total:"POS_total__xKac7",paymentMethods:"POS_paymentMethods__ov_uJ",processPaymentBtn:"POS_processPaymentBtn__C3Gwh",loadingContainer:"POS_loadingContainer__kkXgY",loadingSpinner:"POS_loadingSpinner__6iT1G",spin:"POS_spin__lN_rC",tipContainer:"POS_tipContainer__WAda1",tipContent:"POS_tipContent__ZMLeX",tipHeader:"POS_tipHeader__fKlmQ",tipCancelButton:"POS_tipCancelButton__OJGRW",tipError:"POS_tipError__Ud654",tipTypeSelector:"POS_tipTypeSelector__M1AHY",tipTypeButton:"POS_tipTypeButton__XlWOo",active:"POS_active__0NVhY",tipPercentageOptions:"POS_tipPercentageOptions__d73A_",tipCustomAmount:"POS_tipCustomAmount__WgJfq",tipPercentageGrid:"POS_tipPercentageGrid__89Hw3",tipPercentageButton:"POS_tipPercentageButton__d_62n",tipAmount:"POS_tipAmount__akuv3",customPercentageInput:"POS_customPercentageInput__aU4YQ",customAmountInput:"POS_customAmountInput__6_IUM",currencySymbol:"POS_currencySymbol__eHGaF",tipSummary:"POS_tipSummary__4XL8d",tipSummaryRow:"POS_tipSummaryRow___7Qgq",tipActions:"POS_tipActions__GvIDY",tipConfirmButton:"POS_tipConfirmButton__BbmXo"}},519:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>v,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>b,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>N,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>j});var i=s(7093),n=s(5244),r=s(1323),l=s(2899),o=s.n(l),c=s(6814),d=s(4679),m=e([c,d]);[c,d]=m.then?(await m)():m;let u=(0,r.l)(d,"default"),p=(0,r.l)(d,"getStaticProps"),h=(0,r.l)(d,"getStaticPaths"),v=(0,r.l)(d,"getServerSideProps"),x=(0,r.l)(d,"config"),g=(0,r.l)(d,"reportWebVitals"),j=(0,r.l)(d,"unstable_getStaticProps"),_=(0,r.l)(d,"unstable_getStaticPaths"),y=(0,r.l)(d,"unstable_getStaticParams"),S=(0,r.l)(d,"unstable_getServerProps"),N=(0,r.l)(d,"unstable_getServerSideProps"),b=new i.PagesRouteModule({definition:{kind:n.x.PAGES,page:"/admin/pos",pathname:"/admin/pos",bundlePath:"",filename:""},components:{App:c.default,Document:o()},userland:d});a()}catch(e){a(e)}})},583:(e,t,s)=>{"use strict";s.d(t,{Z:()=>g});var a=s(997),i=s(6689),n=s(1073),r=s.n(n);function l({baseAmount:e,paymentMethod:t,onTipCalculated:s,onCancel:n,customerName:l="Customer"}){let[o,c]=(0,i.useState)(null),[d,m]=(0,i.useState)("percentage"),[u,p]=(0,i.useState)(null),[h,v]=(0,i.useState)(""),[x,g]=(0,i.useState)(""),[j,_]=(0,i.useState)(0),[y,S]=(0,i.useState)(!0),[N,b]=(0,i.useState)(null),C=()=>{s({tipAmount:0,tipMethod:"none",tipPercentage:0,baseAmount:e,totalAmount:e})},f=e=>`$${e.toFixed(2)}`;return y?a.jsx("div",{className:r().tipContainer,children:a.jsx("div",{className:r().tipHeader,children:a.jsx("h3",{children:"Loading tip options..."})})}):o?.enableTips!=="true"?(C(),null):(0,a.jsxs)("div",{className:r().tipContainer,children:[(0,a.jsxs)("div",{className:r().tipHeader,children:[(0,a.jsxs)("h3",{children:["\uD83D\uDCB0 Add Tip for ",l]}),(0,a.jsxs)("p",{children:["Service Total: ",f(e)]}),a.jsx("button",{className:r().tipCancelButton,onClick:n,children:"✕"})]}),N&&a.jsx("div",{className:r().tipError,children:N}),(0,a.jsxs)("div",{className:r().tipContent,children:[(0,a.jsxs)("div",{className:r().tipTypeSelector,children:[a.jsx("button",{className:`${r().tipTypeButton} ${"none"===d?r().active:""}`,onClick:()=>m("none"),children:"No Tip"}),a.jsx("button",{className:`${r().tipTypeButton} ${"percentage"===d?r().active:""}`,onClick:()=>m("percentage"),children:"Percentage"}),o?.customTipAllowed==="true"&&a.jsx("button",{className:`${r().tipTypeButton} ${"amount"===d?r().active:""}`,onClick:()=>m("amount"),children:"Custom Amount"})]}),"percentage"===d&&(0,a.jsxs)("div",{className:r().tipPercentageOptions,children:[a.jsx("h4",{children:"Select Tip Percentage"}),a.jsx("div",{className:r().tipPercentageGrid,children:(o?.defaultTipPercentages?o.defaultTipPercentages.split(",").map(e=>parseInt(e.trim())):[15,18,20,25]).map(t=>(0,a.jsxs)("button",{className:`${r().tipPercentageButton} ${u===t?r().active:""}`,onClick:()=>{p(t),g("")},children:[t,"%",a.jsx("span",{className:r().tipAmount,children:f(e*t/100)})]},t))}),o?.customTipAllowed==="true"&&(0,a.jsxs)("div",{className:r().customPercentageInput,children:[a.jsx("label",{children:"Custom Percentage:"}),a.jsx("input",{type:"number",min:"0",max:o?.maximumTipPercentage||"50",step:"0.1",value:x,onChange:e=>{g(e.target.value),p(null)},placeholder:"Enter %"}),a.jsx("span",{children:"%"})]})]}),"amount"===d&&(0,a.jsxs)("div",{className:r().tipCustomAmount,children:[a.jsx("h4",{children:"Enter Tip Amount"}),(0,a.jsxs)("div",{className:r().customAmountInput,children:[a.jsx("span",{className:r().currencySymbol,children:"$"}),a.jsx("input",{type:"number",min:o?.minimumTipAmount||"1.00",step:"0.01",value:h,onChange:e=>v(e.target.value),placeholder:"0.00"})]})]}),"none"!==d&&j>0&&(0,a.jsxs)("div",{className:r().tipSummary,children:[(0,a.jsxs)("div",{className:r().tipSummaryRow,children:[a.jsx("span",{children:"Service Total:"}),a.jsx("span",{children:f(e)})]}),(0,a.jsxs)("div",{className:r().tipSummaryRow,children:[a.jsx("span",{children:"Tip Amount:"}),a.jsx("span",{children:f(j)})]}),(0,a.jsxs)("div",{className:`${r().tipSummaryRow} ${r().total}`,children:[a.jsx("span",{children:"Total with Tip:"}),a.jsx("span",{children:f(e+j)})]})]}),a.jsx("div",{className:r().tipActions,children:"none"===d?a.jsx("button",{className:r().tipConfirmButton,onClick:C,children:"Continue Without Tip"}):(0,a.jsxs)("button",{className:r().tipConfirmButton,onClick:()=>{s({tipAmount:j,tipMethod:"cash"===t?"cash":"card",tipPercentage:"percentage"===d?u||parseFloat(x):j/e*100,baseAmount:e,totalAmount:e+j})},disabled:j<=0,children:["Add ",f(j)," Tip"]})})]})]})}function o({amount:e,onMethodSelect:t,onCancel:s,customerName:n="Customer"}){let[o,c]=(0,i.useState)(null),[d,m]=(0,i.useState)(""),[u,p]=(0,i.useState)(!1),[h,v]=(0,i.useState)(!1),[x,g]=(0,i.useState)(null),[j,_]=(0,i.useState)(null),y=parseFloat(e||0),S=.029*y+.3,N=.026*y+.1,b=y+S,C=y+N,f=e=>{c(e),"cash"!==e&&(g(e),_({originalAmount:y,processingFee:"square_payment"===e?S:"square_terminal"===e?N:0,totalAmount:"square_payment"===e?b:"square_terminal"===e?C:y}),v(!0))},P=e=>`$${e.toFixed(2)}`;return u?a.jsx("div",{className:r().paymentMethodContainer,children:(0,a.jsxs)("div",{className:r().processingPayment,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Processing payment..."})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:r().paymentMethodContainer,children:[(0,a.jsxs)("div",{className:r().paymentHeader,children:[a.jsx("h3",{children:"Select Payment Method"}),(0,a.jsxs)("div",{className:r().totalAmount,children:["Service Total: ",P(y)]})]}),(0,a.jsxs)("div",{className:r().paymentMethods,children:[(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"square_payment"===o?r().selected:""}`,onClick:()=>f("square_payment"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCB3"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Card Payment"}),a.jsx("p",{children:"Credit/Debit Card Entry"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(y)]}),(0,a.jsxs)("span",{children:["Processing: ",P(S)]}),(0,a.jsxs)("strong",{children:["Total: ",P(b)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"Online"})})]}),(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"square_terminal"===o?r().selected:""}`,onClick:()=>f("square_terminal"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCF1"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Square Terminal"}),a.jsx("p",{children:"Hardware Card Reader"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(y)]}),(0,a.jsxs)("span",{children:["Processing: ",P(N)]}),(0,a.jsxs)("strong",{children:["Total: ",P(C)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"Hardware"})})]}),(0,a.jsxs)("div",{className:`${r().paymentMethodCard} ${"cash"===o?r().selected:""}`,onClick:()=>c("cash"),children:[a.jsx("div",{className:r().methodIcon,children:"\uD83D\uDCB5"}),(0,a.jsxs)("div",{className:r().methodInfo,children:[a.jsx("h4",{children:"Cash Payment"}),a.jsx("p",{children:"Physical Currency"}),a.jsx("div",{className:r().methodPricing,children:(0,a.jsxs)("div",{className:r().priceBreakdown,children:[(0,a.jsxs)("span",{children:["Service: ",P(y)]}),(0,a.jsxs)("span",{children:["Processing: ",P(0)]}),(0,a.jsxs)("strong",{children:["Total: ",P(y)]})]})})]}),a.jsx("div",{className:r().methodBadge,children:a.jsx("span",{children:"No Fees"})})]})]}),"cash"===o&&(0,a.jsxs)("div",{className:r().cashPaymentInterface,children:[a.jsx("h4",{children:"Cash Payment Details"}),(0,a.jsxs)("div",{className:r().cashInputs,children:[a.jsx("div",{className:r().inputGroup,children:(0,a.jsxs)("label",{children:["Amount Due: ",P(y)]})}),(0,a.jsxs)("div",{className:r().inputGroup,children:[a.jsx("label",{children:"Cash Received:"}),a.jsx("input",{type:"number",step:"0.01",min:y,value:d,onChange:e=>m(e.target.value),placeholder:y.toFixed(2),className:r().cashInput})]}),d&&parseFloat(d)>=y&&a.jsx("div",{className:r().changeAmount,children:(0,a.jsxs)("strong",{children:["Change: ",P(parseFloat(d)-y)]})})]}),a.jsx("div",{className:r().cashActions,children:a.jsx("button",{onClick:()=>{let e=parseFloat(d);if(!e||e<y){alert(`Please enter at least $${y.toFixed(2)}`);return}let t=e-y;g("cash"),_({originalAmount:y,processingFee:0,totalAmount:y,cashReceived:e,changeAmount:t}),v(!0)},disabled:!d||parseFloat(d)<y,className:r().processCashButton,children:"Process Cash Payment"})})]}),a.jsx("div",{className:r().paymentActions,children:a.jsx("button",{onClick:s,className:r().cancelButton,children:"Cancel"})}),a.jsx("div",{className:r().paymentNote,children:a.jsx("p",{children:"\uD83D\uDCA1 Processing fees are automatically calculated and included in the total"})})]}),h&&a.jsx(l,{baseAmount:j?.totalAmount||y,paymentMethod:x,customerName:n,onTipCalculated:e=>{v(!1),p(!0),t(x,{...j,tipAmount:e.tipAmount,tipMethod:e.tipMethod,tipPercentage:e.tipPercentage,totalAmount:e.totalAmount})},onCancel:()=>{v(!1),g(null),_(null),c(null)}})]})}function c({amount:e,currency:t="AUD",onSuccess:n,onError:l,orderDetails:o={}}){let[c,d]=(0,i.useState)(null),[m,u]=(0,i.useState)(!0),[p,h]=(0,i.useState)(!1),[v,x]=(0,i.useState)(""),g=(0,i.useRef)(!1),[j,_]=(0,i.useState)({addressLine1:"1455 Market St",addressLine2:"Suite 600",locality:"San Francisco",administrativeDistrictLevel1:"CA",postalCode:"94103",country:"US"}),[y,S]=(0,i.useState)(!1),N=(0,i.useRef)(null),b=(0,i.useRef)(!1),C=(0,i.useRef)(null),f=(0,i.useRef)(!1);(0,i.useRef)(null),(0,i.useRef)(null);let P=(0,i.useCallback)(async()=>{performance.now();try{if(f.current=!0,!b.current){console.warn("InitializeSquareForm: Component unmounted before starting."),f.current=!1;return}if(g.current){console.log("InitializeSquareForm: Already attempted in this lifecycle."),f.current=!1;return}if(!N.current){console.error("InitializeSquareForm: Container ref not available"),f.current=!1;return}throw x(""),Error("Square SDK not loaded")}catch(e){console.error("Square form initialization error:",e),x(e.message||"Failed to initialize payment form"),u(!1)}finally{f.current=!1}},[]),k=()=>{g.current=!1,x(""),u(!0),P()},w=(0,i.useCallback)(async()=>{if(c&&!p){h(!0),x("");try{try{let{startPOSPaymentOperation:e}=await s.e(5815).then(s.bind(s,5815));e()}catch(e){console.warn("POS payment protection not available:",e)}console.log("\uD83D\uDD04 Tokenizing card...");let e=y?{billingContact:{addressLines:[j.addressLine1,j.addressLine2].filter(Boolean),city:j.locality,countryCode:j.country,postalCode:j.postalCode,state:j.administrativeDistrictLevel1}}:{},t=await c.tokenize(e);if("OK"===t.status){console.log("✅ Card tokenized successfully");let e=await $(t.token);n(e)}else{console.error("❌ Tokenization failed:",t.errors);let e=t.errors?.[0]?.message||"Card tokenization failed";x(e),l(Error(e))}}catch(e){console.error("Payment processing error:",e),x(e.message||"Payment failed. Please try again."),l(e)}finally{h(!1);try{let{endPOSPaymentOperation:e}=await s.e(5815).then(s.bind(s,5815));e()}catch(e){console.warn("Error ending POS payment protection:",e)}}}},[p,y,j,e,t,n,l]),$=async s=>{console.log("\uD83D\uDD04 Processing payment with token...");try{let a=await fetch("/api/admin/pos/process-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sourceId:s,amount:Math.round(100*parseFloat(e)),currency:t,orderDetails:o,idempotencyKey:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`})});if(!a.ok){let e=await a.json();throw Error(e.message||`Payment failed: ${a.status}`)}let i=await a.json();return console.log("✅ Payment processed successfully:",i),i}catch(e){throw console.error("Payment API error:",e),e}};return console.log("\uD83D\uDD0D POSSquarePayment render state:",{showBillingAddress:y,isLoading:m,paymentForm:!!C.current,squareSDKLoaded:!1,initializationAttempted:g.current}),(0,a.jsxs)("div",{className:r().squarePaymentContainer,children:[(0,a.jsxs)("div",{className:r().paymentFormHeader,children:[a.jsx("h4",{children:"Card Payment"}),(0,a.jsxs)("div",{className:r().paymentAmount,children:["Amount: ",(0,a.jsxs)("span",{children:["$",parseFloat(e||0).toFixed(2)," ",t]})]})]}),v&&(0,a.jsxs)("div",{className:r().paymentError,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),(0,a.jsxs)("div",{className:r().errorContent,children:[a.jsx("div",{className:r().errorText,children:v}),a.jsx("button",{onClick:k,className:r().retryButton,children:"Retry"})]})]}),(0,a.jsxs)("div",{className:r().cardFormContainer,children:[a.jsx("div",{ref:N,className:r().cardForm,style:{minHeight:"60px",border:"1px solid #e0e0e0",borderRadius:"8px",padding:"16px",background:"white"},children:m&&(0,a.jsxs)("div",{className:r().cardFormPlaceholder,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Initializing secure payment form..."})]})}),y&&(0,a.jsxs)("div",{className:r().billingAddressSection,children:[a.jsx("h5",{children:"Billing Address"}),(0,a.jsxs)("div",{className:r().addressGrid,children:[(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Address Line 1"}),a.jsx("input",{type:"text",value:j.addressLine1,onChange:e=>_(t=>({...t,addressLine1:e.target.value})),placeholder:"1455 Market St"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Address Line 2"}),a.jsx("input",{type:"text",value:j.addressLine2,onChange:e=>_(t=>({...t,addressLine2:e.target.value})),placeholder:"Suite 600"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"City"}),a.jsx("input",{type:"text",value:j.locality,onChange:e=>_(t=>({...t,locality:e.target.value})),placeholder:"San Francisco"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"State"}),a.jsx("input",{type:"text",value:j.administrativeDistrictLevel1,onChange:e=>_(t=>({...t,administrativeDistrictLevel1:e.target.value})),placeholder:"CA"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"ZIP Code"}),a.jsx("input",{type:"text",value:j.postalCode,onChange:e=>_(t=>({...t,postalCode:e.target.value})),placeholder:"94103"})]}),(0,a.jsxs)("div",{className:r().addressField,children:[a.jsx("label",{children:"Country"}),(0,a.jsxs)("select",{value:j.country,onChange:e=>_(t=>({...t,country:e.target.value})),children:[a.jsx("option",{value:"US",children:"United States"}),a.jsx("option",{value:"AU",children:"Australia"}),a.jsx("option",{value:"CA",children:"Canada"}),a.jsx("option",{value:"GB",children:"United Kingdom"})]})]})]}),a.jsx("div",{className:r().addressNote,children:a.jsx("small",{children:"\uD83D\uDCA1 Billing address is required for card verification in sandbox mode"})})]})]}),(0,a.jsxs)("div",{className:r().paymentActions,children:[a.jsx("button",{onClick:w,disabled:!c||p||m,className:r().payButton,children:p?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:r().buttonSpinner}),"Processing..."]}):`Pay ${t} $${parseFloat(e||0).toFixed(2)}`}),!c&&!m&&!v&&(0,a.jsxs)("div",{className:r().formNotReady,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),"Card form not initialized. Please wait for the form to load."]}),v&&(0,a.jsxs)("div",{className:r().errorContainer,children:[a.jsx("p",{className:r().errorMessage,children:v}),a.jsx("button",{onClick:k,className:r().retryButton,children:"Retry"})]})]}),(0,a.jsxs)("div",{className:r().paymentSecurity,children:[(0,a.jsxs)("div",{className:r().securityBadges,children:[a.jsx("span",{className:r().securityBadge,children:"\uD83D\uDD12 SSL Encrypted"}),a.jsx("span",{className:r().securityBadge,children:"✅ PCI Compliant"}),a.jsx("span",{className:r().securityBadge,children:"\uD83D\uDEE1️ Square Secure"})]}),a.jsx("p",{className:r().securityText,children:"Your payment information is processed securely by Square and never stored on our servers."})]})]})}function d({amount:e,currency:t="AUD",orderDetails:s,onSuccess:n,onError:l,onCancel:o}){let[c,d]=(0,i.useState)([]),[m,u]=(0,i.useState)(null),[p,h]=(0,i.useState)(!1),[v,x]=(0,i.useState)(null),[g,j]=(0,i.useState)(null),[_,y]=(0,i.useState)(""),[S,N]=(0,i.useState)(!0),b=async()=>{try{N(!0);let e=await fetch("/api/admin/pos/terminal-devices",{headers:{Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});if(e.ok){let t=await e.json();d(t.devices||[])}else console.error("Failed to load terminal devices"),d([])}catch(e){console.error("Error loading terminal devices:",e),d([])}finally{N(!1)}},C=async()=>{if(!m){y("Please select a terminal device");return}try{h(!0),y("");let a=await fetch("/api/admin/pos/terminal-checkout",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("adminToken")}`},body:JSON.stringify({deviceId:m.id,amountMoney:{amount:Math.round(100*parseFloat(e)),currency:t},note:s?.service||"POS Payment",orderId:s?.orderId||`pos_${Date.now()}`,paymentOptions:{autocomplete:!0,collectSignature:!0,allowTipping:!1}})});if(!a.ok){let e=await a.json();throw Error(e.message||"Failed to create terminal checkout")}let i=await a.json();x(i.checkout.id),j(i.checkout.status),console.log("Terminal checkout created:",i.checkout)}catch(e){console.error("Error creating terminal checkout:",e),y(e.message),h(!1),l(e)}};return S?a.jsx("div",{className:r().terminalPaymentContainer,children:(0,a.jsxs)("div",{className:r().loadingState,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Loading terminal devices..."})]})}):0===c.length?a.jsx("div",{className:r().terminalPaymentContainer,children:(0,a.jsxs)("div",{className:r().noDevicesState,children:[a.jsx("div",{className:r().noDevicesIcon,children:"\uD83D\uDCF1"}),a.jsx("h3",{children:"No Terminal Devices Available"}),a.jsx("p",{children:"No paired Square Terminal devices found. Please pair a device first."}),a.jsx("button",{className:r().refreshButton,onClick:b,children:"Refresh Devices"})]})}):(0,a.jsxs)("div",{className:r().terminalPaymentContainer,children:[(0,a.jsxs)("div",{className:r().terminalHeader,children:[a.jsx("h3",{children:"Square Terminal Payment"}),(0,a.jsxs)("div",{className:r().paymentAmount,children:["$",parseFloat(e||0).toFixed(2)," ",t]})]}),!p&&(0,a.jsxs)("div",{className:r().deviceSelection,children:[a.jsx("h4",{children:"Select Terminal Device"}),a.jsx("div",{className:r().deviceList,children:c.map(e=>(0,a.jsxs)("div",{className:`${r().deviceCard} ${m?.id===e.id?r().selected:""}`,onClick:()=>u(e),children:[a.jsx("div",{className:r().deviceIcon,children:"\uD83D\uDCF1"}),(0,a.jsxs)("div",{className:r().deviceInfo,children:[a.jsx("div",{className:r().deviceName,children:e.name||`Terminal ${e.id.slice(-4)}`}),(0,a.jsxs)("div",{className:r().deviceStatus,children:["\uD83D\uDFE2 ",e.status]})]})]},e.id))})]}),p&&(0,a.jsxs)("div",{className:r().processingState,children:[a.jsx("div",{className:r().statusIcon,children:(()=>{switch(g){case"PENDING":case"IN_PROGRESS":return"⏳";case"COMPLETED":return"✅";case"CANCELED":case"ERROR":return"❌";default:return"\uD83D\uDCF1"}})()}),a.jsx("div",{className:r().statusMessage,children:(()=>{switch(g){case"PENDING":return"Waiting for customer to complete payment on terminal...";case"IN_PROGRESS":return"Payment in progress on terminal...";case"COMPLETED":return"Payment completed successfully!";case"CANCELED":return"Payment was cancelled";case"ERROR":return"Payment failed";default:return"Preparing terminal checkout..."}})()}),"PENDING"===g&&(0,a.jsxs)("div",{className:r().terminalInstructions,children:[a.jsx("p",{children:"Customer should now see the payment screen on the terminal device."}),a.jsx("p",{children:"They can insert their card, tap for contactless, or use mobile payment."})]})]}),_&&(0,a.jsxs)("div",{className:r().errorMessage,children:[a.jsx("div",{className:r().errorIcon,children:"⚠️"}),a.jsx("div",{className:r().errorText,children:_})]}),a.jsx("div",{className:r().terminalActions,children:p?a.jsx("button",{className:r().cancelButton,onClick:()=>{h(!1),x(null),j(null),o()},children:"Cancel Payment"}):a.jsx("button",{className:r().startPaymentButton,onClick:C,disabled:!m,children:"Start Terminal Payment"})})]})}var m=s(8316);async function u(e,t=null){try{let s=await p(t);if(!s){console.log("No template found, using default template");let t=h(),s=v(t,e);return{success:!0,html:s,template:t}}let a=v(s,e);return{success:!0,html:a,template:s}}catch(t){console.error("Error generating receipt:",t);try{console.log("Falling back to default template due to error");let t=h(),s=v(t,e);return{success:!0,html:s,template:t}}catch(e){return console.error("Fallback template generation failed:",e),{success:!1,error:t.message}}}}async function p(e=null){try{let t=m.pR.from("receipt_templates").select("*").eq("is_active",!0);t=e?t.eq("id",e):t.eq("is_default",!0);let{data:s,error:a}=await t.limit(1);if(a){if("42P01"===a.code)return console.log("Receipt templates table not found, using default template"),h();throw Error(`Database error: ${a.message}`)}if(!s||0===s.length){let{data:e}=await m.pR.from("receipt_templates").select("*").eq("is_active",!0).limit(1);return e?.[0]||h()}return s[0]}catch(e){return console.error("Error fetching receipt template:",e),h()}}function h(){return{id:"default-standard",name:"Standard Receipt",description:"Default receipt template",template_type:"standard",is_default:!0,is_active:!0,business_name:"Ocean Soul Sparkles",business_address:"Australia",business_phone:"+61 XXX XXX XXX",business_email:"<EMAIL>",business_website:"oceansoulsparkles.com.au",business_abn:"",show_logo:!0,logo_position:"center",header_color:"#667eea",text_color:"#333333",font_family:"Arial",font_size:12,show_customer_details:!0,show_service_details:!0,show_artist_details:!0,show_payment_details:!0,show_booking_notes:!1,show_terms_conditions:!0,footer_message:"Thank you for choosing Ocean Soul Sparkles!",show_social_media:!1,social_media_links:null,custom_fields:[]}}function v(e,t){let{business_name:s,business_address:a,business_phone:i,business_email:n,business_website:r,business_abn:l,show_logo:o,logo_position:c,header_color:d,text_color:m,font_family:u,font_size:p,show_customer_details:h,show_service_details:v,show_artist_details:x,show_payment_details:g,show_booking_notes:j,show_terms_conditions:_,footer_message:y,show_social_media:S,social_media_links:N,template_type:b}=e,C=t.receipt_number||`OSS-${Date.now()}`,f=new Date().toLocaleDateString("en-AU",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),P=`
    <style>
      body { 
        font-family: ${u}, sans-serif; 
        font-size: ${p}px; 
        color: ${m}; 
        margin: 0; 
        padding: 20px; 
        line-height: 1.4;
        max-width: 400px;
        margin: 0 auto;
      }
      .receipt-header { 
        text-align: ${c}; 
        margin-bottom: 20px; 
        padding-bottom: 15px;
        border-bottom: 2px solid ${d};
      }
      .business-name { 
        font-size: ${Math.round(1.5*p)}px; 
        font-weight: bold; 
        color: ${d}; 
        margin: 0 0 5px 0;
      }
      .business-info { 
        font-size: ${Math.round(.9*p)}px; 
        color: #666; 
        margin: 2px 0;
      }
      .receipt-title { 
        font-size: ${Math.round(1.2*p)}px; 
        font-weight: bold; 
        text-align: center; 
        margin: 20px 0 15px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .receipt-info { 
        margin-bottom: 20px; 
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
      .section { 
        margin-bottom: 15px; 
      }
      .section-title { 
        font-weight: bold; 
        margin-bottom: 8px; 
        color: ${d};
        font-size: ${Math.round(1.1*p)}px;
      }
      .detail-row { 
        display: flex; 
        justify-content: space-between; 
        margin-bottom: 3px;
        align-items: flex-start;
      }
      .detail-label { 
        font-weight: 500; 
        flex: 1;
      }
      .detail-value { 
        text-align: right; 
        flex: 1;
        word-break: break-word;
      }
      .total-section { 
        border-top: 2px solid ${d}; 
        padding-top: 10px; 
        margin-top: 15px;
      }
      .total-row { 
        display: flex; 
        justify-content: space-between; 
        font-weight: bold; 
        font-size: ${Math.round(1.1*p)}px;
        margin-bottom: 5px;
      }
      .footer { 
        text-align: center; 
        margin-top: 20px; 
        padding-top: 15px; 
        border-top: 1px solid #eee;
        font-size: ${Math.round(.9*p)}px;
      }
      .footer-message { 
        font-style: italic; 
        color: #666; 
        margin-bottom: 10px;
      }
      .compact { font-size: ${Math.round(.9*p)}px; }
      .detailed { font-size: ${p}px; }
      @media print {
        body { margin: 0; padding: 10px; }
        .receipt-header { page-break-inside: avoid; }
      }
    </style>
  `,k=`
    <div class="receipt-header">
      ${o?`<div class="business-name">${s}</div>`:""}
      ${a?`<div class="business-info">${a}</div>`:""}
      ${i?`<div class="business-info">${i}</div>`:""}
      ${n?`<div class="business-info">${n}</div>`:""}
      ${r?`<div class="business-info">${r}</div>`:""}
      ${l?`<div class="business-info">ABN: ${l}</div>`:""}
    </div>
  `,w=`
    <div class="receipt-title">Receipt</div>
    <div class="receipt-info">
      <div class="detail-row">
        <span class="detail-label">Receipt #:</span>
        <span class="detail-value">${C}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">${f}</span>
      </div>
    </div>
  `,$="";h&&t.customer_name&&($=`
      <div class="section">
        <div class="section-title">Customer Details</div>
        <div class="detail-row">
          <span class="detail-label">Name:</span>
          <span class="detail-value">${t.customer_name}</span>
        </div>
        ${t.customer_email?`
        <div class="detail-row">
          <span class="detail-label">Email:</span>
          <span class="detail-value">${t.customer_email}</span>
        </div>`:""}
        ${t.customer_phone?`
        <div class="detail-row">
          <span class="detail-label">Phone:</span>
          <span class="detail-value">${t.customer_phone}</span>
        </div>`:""}
      </div>
    `);let D="";if(v){let e=t.start_time?new Date(t.start_time).toLocaleString("en-AU"):"N/A",s=t.duration?`${t.duration} minutes`:"N/A";D=`
      <div class="section">
        <div class="section-title">Service Details</div>
        <div class="detail-row">
          <span class="detail-label">Service:</span>
          <span class="detail-value">${t.service_name||"N/A"}</span>
        </div>
        ${t.tier_name?`
        <div class="detail-row">
          <span class="detail-label">Tier:</span>
          <span class="detail-value">${t.tier_name}</span>
        </div>`:""}
        <div class="detail-row">
          <span class="detail-label">Date & Time:</span>
          <span class="detail-value">${e}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Duration:</span>
          <span class="detail-value">${s}</span>
        </div>
      </div>
    `}let A="";x&&t.artist_name&&(A=`
      <div class="section">
        <div class="section-title">Artist Details</div>
        <div class="detail-row">
          <span class="detail-label">Artist:</span>
          <span class="detail-value">${t.artist_name}</span>
        </div>
      </div>
    `);let O="";if(g){let e=t.total_amount||0,s=t.tip_amount||0;O=`
      <div class="section">
        <div class="section-title">Payment Details</div>
        ${s>0?`
        <div class="detail-row">
          <span class="detail-label">Subtotal:</span>
          <span class="detail-value">$${(e-s).toFixed(2)}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Tip:</span>
          <span class="detail-value">$${s.toFixed(2)}</span>
        </div>`:""}
        <div class="total-section">
          <div class="total-row">
            <span>Total:</span>
            <span>$${e.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Payment Method:</span>
            <span class="detail-value">${t.payment_method||"N/A"}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">Paid</span>
          </div>
        </div>
      </div>
    `}let I="";j&&t.notes&&(I=`
      <div class="section">
        <div class="section-title">Notes</div>
        <div>${t.notes}</div>
      </div>
    `);let T="";return(y||_)&&(T=`
      <div class="footer">
        ${y?`<div class="footer-message">${y}</div>`:""}
        ${_?`
        <div style="font-size: ${Math.round(.8*p)}px; color: #888;">
          Terms & Conditions apply. Visit our website for details.
        </div>`:""}
        ${S&&N?`
        <div style="margin-top: 10px;">
          Follow us on social media for updates and inspiration!
        </div>`:""}
      </div>
    `),`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Receipt - ${C}</title>
      ${P}
    </head>
    <body class="${b}">
      ${k}
      ${w}
      ${$}
      ${D}
      ${A}
      ${O}
      ${I}
      ${T}
    </body>
    </html>
  `}async function x(e,t=null){let s={receipt_number:e.receiptNumber||`POS-${Date.now()}`,customer_name:e.customerName||"Walk-in Customer",customer_email:e.customerEmail,customer_phone:e.customerPhone,service_name:e.serviceName,tier_name:e.tierName,artist_name:e.artistName,start_time:e.startTime||new Date().toISOString(),duration:e.duration,total_amount:e.totalAmount,tip_amount:e.tipAmount||0,payment_method:e.paymentMethod,notes:e.notes};return await u(s,t)}function g({service:e,artist:t,tier:n,timeSlot:l,onBack:m,onComplete:u}){let[p,h]=(0,i.useState)("customer"),[v,g]=(0,i.useState)(null),[_,y]=(0,i.useState)(null),[S,N]=(0,i.useState)(null),[b,C]=(0,i.useState)(!1),[f,P]=(0,i.useState)(null),k=n?.price||0,w=async e=>{try{C(!0),P(null);let t=await O("cash",{cashReceived:e?.cashReceived,changeAmount:e?.changeAmount,totalAmount:e?.totalAmount||k});if(t.success)await A(t,"cash",e),u(t);else throw Error(t.error||"Failed to process cash payment")}catch(e){console.error("Cash payment error:",e),P(e.message)}finally{C(!1)}},$=async e=>{try{C(!0),P(null);let t=e.paymentDetails?.deviceId?"square_terminal":"card",s=await O(t,e);if(s.success)await A(s,t,e),u(s);else throw Error(s.error||"Failed to record payment")}catch(e){console.error("Payment completion error:",e),P(e.message)}finally{C(!1)}},D=e=>{console.error("Square payment error:",e),P(e.message||"Card payment failed")},A=async(s,a,i)=>{try{let r={receiptNumber:`POS-${Date.now()}`,customerName:v?.name||"Walk-in Customer",customerEmail:v?.email,customerPhone:v?.phone,serviceName:e.name,tierName:n.name,artistName:t.name,startTime:l.time,duration:n.duration,totalAmount:i?.totalAmount||k,tipAmount:i?.tipAmount||0,paymentMethod:a,notes:`POS Transaction - ${t.name} - ${n.name}`},o=await x(r);o.success?(console.log("Receipt generated successfully"),s.receiptHtml=o.html,s.receiptData=r):console.warn("Failed to generate receipt:",o.error)}catch(e){console.error("Error generating receipt:",e)}},O=async(a,i=null)=>{try{console.log("\uD83D\uDD04 Creating POS booking...");let{supabase:r}=await Promise.resolve().then(s.bind(s,8316)),{data:{session:o},error:c}=await r.auth.getSession();if(console.log("Booking authentication check:",{hasSession:!!o,hasUser:!!o?.user,hasToken:!!o?.access_token,userEmail:o?.user?.email,error:c?.message}),!o?.access_token)throw Error("Authentication required. Please refresh the page and try again.");let d={customer_name:v?.name||"Walk-in Customer",customer_email:v?.email||"",customer_phone:v?.phone||"",service_id:e.id,service_name:e.name,artist_id:t.id,artist_name:t.name,tier_id:n.id,tier_name:n.name,duration:n.duration,start_time:l.time,end_time:new Date(new Date(l.time).getTime()+6e4*n.duration).toISOString(),total_amount:S?.totalAmount||k,status:"completed",payment_method:a,payment_status:"completed",notes:`POS Booking - ${t.name} - ${n.name}${S?.tipAmount>0?` - Tip: $${S.tipAmount.toFixed(2)}`:""}`,booking_type:"pos",created_via:"admin_pos",tip_amount:S?.tipAmount||0,tip_method:S?.tipMethod||"none",tip_percentage:S?.tipPercentage||0};i&&("cash"===a?(d.cash_received=i.cashReceived,d.change_amount=i.changeAmount):(d.payment_transaction_id=i.paymentId||i.transactionId,d.processing_fee=S?.processingFee||0)),console.log("Creating booking with data:",d);let m=await fetch("/api/admin/pos/create-booking",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.access_token}`},body:JSON.stringify(d)});if(!m.ok){let e=await m.text();throw console.error("Booking creation failed:",m.status,e),Error(`Failed to create booking: ${m.status}`)}let u=await m.json();return console.log("✅ Booking created successfully:",u),{success:!0,booking:u.booking,payment:u.payment,message:"Booking and payment processed successfully!"}}catch(e){return console.error("Error creating booking:",e),{success:!1,error:e.message}}};return(0,a.jsxs)("div",{className:r().posCheckoutContainer,children:[(0,a.jsxs)("div",{className:r().checkoutHeader,children:[a.jsx("button",{onClick:m,className:r().backButton,children:"← Back"}),a.jsx("h2",{children:"Complete Booking"})]}),(0,a.jsxs)("div",{className:r().bookingSummary,children:[a.jsx("h3",{children:"Booking Details"}),(0,a.jsxs)("div",{className:r().summaryGrid,children:[(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Service:"})," ",e.name]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Artist:"})," ",t.name]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Duration:"})," ",(e=>{if(!e)return"";let t=Math.floor(e/60),s=e%60;return t>0?s>0?`${t}h ${s}m`:`${t}h`:`${s}m`})(n.duration)]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Price:"})," $",n.price?.toFixed(2)]}),(0,a.jsxs)("div",{className:r().summaryItem,children:[a.jsx("strong",{children:"Date & Time:"})," ",new Date(l.time).toLocaleString()]})]})]}),f&&(0,a.jsxs)("div",{className:r().errorAlert,children:[a.jsx("span",{className:r().errorIcon,children:"⚠️"}),a.jsx("div",{className:r().errorText,children:f}),a.jsx("button",{onClick:()=>P(null),className:r().closeError,children:"\xd7"})]}),"customer"===p&&(0,a.jsxs)("div",{className:r().customerStep,children:[a.jsx("h3",{children:"Customer Information"}),a.jsx("div",{className:r().customerForm,children:a.jsx(j,{onSubmit:e=>{g(e),h("payment")}})})]}),"payment"===p&&!_&&a.jsx(o,{amount:k,customerName:v?.name||"Walk-in Customer",onMethodSelect:(e,t)=>{y(e),N(t),"cash"===e&&w(t)},onCancel:()=>h("customer")}),"payment"===p&&"square_payment"===_&&a.jsx(c,{amount:S?.totalAmount||k,currency:"AUD",onSuccess:$,onError:D,orderDetails:{service:e.name,tier:n.name,customer:v?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),"payment"===p&&"square_terminal"===_&&a.jsx(d,{amount:S?.totalAmount||k,currency:"AUD",onSuccess:$,onError:D,onCancel:()=>y(null),orderDetails:{service:e.name,tier:n.name,customer:v?.name||"Walk-in Customer",orderId:`pos_${Date.now()}_${Math.random().toString(36).substring(2,8)}`}}),b&&a.jsx("div",{className:r().processingOverlay,children:(0,a.jsxs)("div",{className:r().processingContent,children:[a.jsx("div",{className:r().loadingSpinner}),a.jsx("p",{children:"Processing booking and payment..."})]})})]})}function j({onSubmit:e}){let[t,s]=(0,i.useState)({name:"",email:"",phone:"",notes:""}),[n,l]=(0,i.useState)(!1),o=(e,t)=>{s(s=>({...s,[e]:t}))};return(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),n)e({name:"Walk-in Customer",email:"",phone:"",notes:t.notes});else{if(!t.name.trim()){alert("Please enter customer name");return}e(t)}},className:r().customerForm,children:[a.jsx("div",{className:r().walkInToggle,children:(0,a.jsxs)("label",{children:[a.jsx("input",{type:"checkbox",checked:n,onChange:e=>l(e.target.checked)}),"Walk-in Customer (no details required)"]})}),!n&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Customer Name *"}),a.jsx("input",{type:"text",value:t.name,onChange:e=>o("name",e.target.value),placeholder:"Enter customer name",required:!0})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Email"}),a.jsx("input",{type:"email",value:t.email,onChange:e=>o("email",e.target.value),placeholder:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Phone"}),a.jsx("input",{type:"tel",value:t.phone,onChange:e=>o("phone",e.target.value),placeholder:"+61 XXX XXX XXX"})]})]}),(0,a.jsxs)("div",{className:r().formGroup,children:[a.jsx("label",{children:"Special Notes"}),a.jsx("textarea",{value:t.notes,onChange:e=>o("notes",e.target.value),placeholder:"Any special requirements or notes...",rows:3})]}),a.jsx("button",{type:"submit",className:r().continueButton,children:"Continue to Payment"})]})}},84:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(997),i=s(6689);function n(e,t=null){try{if("function"==typeof e)return e();return e}catch(e){return console.warn("Safe render error:",e),t}}var r=s(1073),l=s.n(r);function o({service:e,onBookingSlotSelect:t,onBack:s}){let[r,o]=(0,i.useState)(new Date().toISOString().split("T")[0]),[c,d]=(0,i.useState)(null),[m,u]=(0,i.useState)(null),[p,h]=(0,i.useState)([]),[v,x]=(0,i.useState)(!1),[g,j]=(0,i.useState)(null),_=e?.availableArtists?.filter(e=>e.isAvailableToday)||[],y=e?.pricing_tiers||[],S=e=>{d(e)},N=e=>{u(e)},b=e=>{"available"===e.status&&c&&m&&t(c,m,{...e,time:e.time})},C=e=>new Date(e).toLocaleTimeString("en-AU",{hour:"2-digit",minute:"2-digit",hour12:!1}),f=e=>`$${parseFloat(e||0).toFixed(2)}`,P=c&&m;return(0,a.jsxs)("div",{className:l().serviceBookingAvailability,children:[(0,a.jsxs)("div",{className:l().bookingHeader,children:[a.jsx("button",{className:l().backButton,onClick:s,children:"← Back to Services"}),(0,a.jsxs)("div",{className:l().serviceInfo,children:[a.jsx("h2",{className:l().serviceName,children:n(e.name,"Service")}),a.jsx("p",{className:l().serviceDescription,children:n(e.description,"")})]})]}),(0,a.jsxs)("div",{className:l().bookingSelectionGrid,children:[(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Artist"}),0===_.length?a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No artists available for this service today"})}):a.jsx("div",{className:l().artistGrid,children:_.map(e=>(0,a.jsxs)("div",{className:`${l().artistCard} ${c?.id===e.id?l().selected:""}`,onClick:()=>S(e),children:[a.jsx("div",{className:l().artistAvatar,children:e.name?.charAt(0)?.toUpperCase()||"?"}),(0,a.jsxs)("div",{className:l().artistInfo,children:[a.jsx("h4",{className:l().artistName,children:n(e.name,"Unknown Artist")}),a.jsx("p",{className:l().artistRole,children:n(e.role||e.specialties?.[0],"Artist")})]})]},e.id))})]}),(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Duration & Price"}),0===y.length?a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No pricing options available"})}):a.jsx("div",{className:l().tierGrid,children:y.map(e=>(0,a.jsxs)("div",{className:`${l().tierCard} ${m?.id===e.id?l().selected:""}`,onClick:()=>N(e),children:[a.jsx("h4",{className:l().tierName,children:n(e.name,"Standard")}),a.jsx("div",{className:l().tierPrice,children:f(e.price)}),(0,a.jsxs)("div",{className:l().tierDuration,children:[n(e.duration,"30")," minutes"]}),e.description&&a.jsx("p",{className:l().tierDescription,children:e.description})]},e.id))})]}),(0,a.jsxs)("div",{className:l().selectionSection,children:[a.jsx("h3",{className:l().sectionTitle,children:"Choose Time Slot"}),(0,a.jsxs)("div",{className:l().dateSelector,children:[a.jsx("label",{htmlFor:"booking-date-select",children:"Date:"}),a.jsx("input",{id:"booking-date-select",type:"date",value:r,onChange:e=>{o(e.target.value)},className:l().dateInput,min:new Date().toISOString().split("T")[0]})]}),P?v?(0,a.jsxs)("div",{className:l().loading,children:[a.jsx("div",{className:l().loadingSpinner}),a.jsx("p",{children:"Loading time slots..."})]}):g?a.jsx("div",{className:`${l().errorNotice} p-3 bg-red-100 border border-red-400 text-red-700 rounded-md`,children:(0,a.jsxs)("p",{children:["Error: ",g]})}):p.length>0?a.jsx("div",{className:l().timeSlots,children:p.map(e=>a.jsx("div",{className:`${l().timeSlot} ${l()[e.status]||("available"===e.status?l().available:l().unavailable)} ${"available"===e.status?l().clickable:""}`,onClick:()=>b(e),title:`${C(e.time)} - ${e.status}`,children:C(e.time)},e.id||e.time))}):a.jsx("div",{className:l().noOptions,children:a.jsx("p",{children:"No available slots for this day, artist, or service duration. Try changing the date or service options."})}):a.jsx("div",{className:l().selectPrompt,children:a.jsx("p",{children:"Please select an artist and duration first"})})]})]}),(c||m)&&(0,a.jsxs)("div",{className:l().selectionSummary,children:[a.jsx("h4",{children:"Current Selection"}),(0,a.jsxs)("div",{className:l().summaryGrid,children:[c&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Artist:"})," ",c.name]}),m&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Duration:"})," ",m.duration," minutes"]}),m&&(0,a.jsxs)("div",{className:l().summaryItem,children:[a.jsx("strong",{children:"Price:"})," ",f(m.price)]})]}),P&&a.jsx("p",{className:l().nextStep,children:"Now select an available time slot to continue"})]})]})}},8316:(e,t,s)=>{"use strict";s.d(t,{supabase:()=>l,pR:()=>o});let a=require("@supabase/supabase-js"),i="https://ndlgbcsbidyhxbpqzgqp.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI",r=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!n)throw Error("Missing Supabase environment variables");let l=(0,a.createClient)(i,n,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}}),o=(0,a.createClient)(i,r||n,{auth:{autoRefreshToken:!1,persistSession:!1}})},4679:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>v});var i=s(997),n=s(6689),r=s(968),l=s.n(r),o=s(8568),c=s(4845),d=s(84),m=s(583),u=s(1073),p=s.n(u),h=e([c]);function v(){let{user:e,loading:t}=(0,o.a)(),[s,a]=(0,n.useState)(!0),[r,u]=(0,n.useState)([]),[h,v]=(0,n.useState)([]),[x,g]=(0,n.useState)("services"),[j,_]=(0,n.useState)(null),[y,S]=(0,n.useState)(null),[N,b]=(0,n.useState)([]),[C,f]=(0,n.useState)(null),[P,k]=(0,n.useState)(0),[w,$]=(0,n.useState)("card"),[D,A]=(0,n.useState)(!1),[O,I]=(0,n.useState)(""),[T,B]=(0,n.useState)(""),F=e=>{_(e),g("booking")},q=e=>{N.find(t=>t.id===e.id)?b(N.map(t=>t.id===e.id?{...t,quantity:t.quantity+1}:t)):b([...N,{id:e.id,name:e.name,price:e.price||0,quantity:1,duration:e.duration}])},E=e=>{b(N.filter(t=>t.id!==e))},M=(e,t)=>{if(t<=0){E(e);return}b(N.map(s=>s.id===e?{...s,quantity:t}:s))},R=()=>{b([]),f(null),k(0)},L=async()=>{if(0!==N.length){A(!0);try{let e=localStorage.getItem("admin-token"),t={customer_id:C?.id,customer_name:C?.name||"Walk-in Customer",customer_email:C?.email||"",customer_phone:C?.phone||"",services:N.map(e=>({service_id:e.id,service_name:e.name,quantity:e.quantity,price:e.price,duration:e.duration})),total_amount:P,payment_method:w,status:"completed",booking_date:new Date().toISOString(),notes:"POS Transaction"};if((await fetch("/api/admin/bookings",{method:"POST",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)alert("Transaction completed successfully!"),R();else throw Error("Failed to process transaction")}catch(e){console.error("Error processing transaction:",e),alert("Error processing transaction. Please try again.")}finally{A(!1)}}},z=e=>new Intl.NumberFormat("en-AU",{style:"currency",currency:"AUD"}).format(e),G=e=>{if(!e)return"";if(e>=60){let t=Math.floor(e/60),s=e%60;return s>0?`${t}h ${s}m`:`${t}h`}return`${e}m`},X=r.filter(e=>e.name.toLowerCase().includes(O.toLowerCase())||e.category.toLowerCase().includes(O.toLowerCase()));return(h.filter(e=>e.name.toLowerCase().includes(T.toLowerCase())||e.email.toLowerCase().includes(T.toLowerCase())||e.phone&&e.phone.includes(T)),t||s)?i.jsx(c.Z,{children:(0,i.jsxs)("div",{className:p().loadingContainer,children:[i.jsx("div",{className:p().loadingSpinner}),i.jsx("p",{children:"Loading POS system..."})]})}):e?(0,i.jsxs)(c.Z,{children:[(0,i.jsxs)(l(),{children:[i.jsx("title",{children:"Point of Sale | Ocean Soul Sparkles Admin"}),i.jsx("meta",{name:"description",content:"Process transactions and manage sales"})]}),(0,i.jsxs)("div",{className:p().posContainer,children:["services"===x&&(0,i.jsxs)("div",{className:p().posContent,children:[(0,i.jsxs)("div",{className:p().servicesSection,children:[(0,i.jsxs)("div",{className:p().sectionHeader,children:[i.jsx("h2",{children:"Services"}),i.jsx("p",{children:"Select a service to start booking with calendar and payment processing"}),i.jsx("input",{type:"text",placeholder:"Search services...",value:O,onChange:e=>I(e.target.value),className:p().searchInput})]}),i.jsx("div",{className:p().servicesGrid,children:X.map(e=>(0,i.jsxs)("div",{className:p().serviceCard,children:[i.jsx("h3",{className:p().serviceName,children:e.name}),i.jsx("p",{className:p().serviceCategory,children:e.category}),(0,i.jsxs)("div",{className:p().serviceDetails,children:[i.jsx("span",{className:p().price,children:e.price?z(e.price):"From $50"}),e.duration&&i.jsx("span",{className:p().duration,children:G(e.duration)})]}),(0,i.jsxs)("div",{className:p().serviceActions,children:[i.jsx("button",{onClick:()=>F(e),className:p().bookServiceBtn,children:"\uD83D\uDCC5 Book with Calendar"}),i.jsx("button",{onClick:()=>q(e),className:p().addToCartBtn,disabled:!e.price,children:"\uD83D\uDED2 Quick Add"})]})]},e.id))})]}),N.length>0&&(0,i.jsxs)("div",{className:p().quickCart,children:[(0,i.jsxs)("h3",{children:["Quick Cart (",N.length," items)"]}),i.jsx("div",{className:p().cartItems,children:N.map(e=>(0,i.jsxs)("div",{className:p().cartItem,children:[(0,i.jsxs)("div",{className:p().itemInfo,children:[i.jsx("span",{className:p().itemName,children:e.name}),i.jsx("span",{className:p().itemPrice,children:z(e.price)})]}),(0,i.jsxs)("div",{className:p().quantityControls,children:[i.jsx("button",{onClick:()=>M(e.id,e.quantity-1),className:p().quantityBtn,children:"-"}),i.jsx("span",{className:p().quantity,children:e.quantity}),i.jsx("button",{onClick:()=>M(e.id,e.quantity+1),className:p().quantityBtn,children:"+"})]}),i.jsx("span",{className:p().itemTotal,children:z(e.price*e.quantity)}),i.jsx("button",{onClick:()=>E(e.id),className:p().removeBtn,children:"\xd7"})]},e.id))}),(0,i.jsxs)("div",{className:p().cartTotal,children:["Total: ",z(P)]}),(0,i.jsxs)("div",{className:p().cartActions,children:[i.jsx("button",{onClick:R,className:p().clearBtn,children:"Clear"}),i.jsx("button",{onClick:L,className:p().quickCheckoutBtn,disabled:D,children:D?"Processing...":"Quick Checkout"})]})]})]}),"booking"===x&&j&&i.jsx(d.Z,{service:j,onBookingSlotSelect:(e,t,s)=>{S({artist:e,tier:t,timeSlot:s}),g("checkout")},onBack:()=>{g("services"),_(null),S(null)}}),"checkout"===x&&y&&i.jsx(m.Z,{service:j,artist:y.artist,tier:y.tier,timeSlot:y.timeSlot,onBack:()=>{g("booking"),S(null)},onComplete:e=>{console.log("Checkout completed:",e),g("services"),_(null),S(null),alert("Booking completed successfully!")}})]})]}):null}c=(h.then?(await h)():h)[0],a()}catch(e){a(e)}})},2785:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{"use strict";e.exports=require("next/head")},6689:e=>{"use strict";e.exports=require("react")},6405:e=>{"use strict";e.exports=require("react-dom")},997:e=>{"use strict";e.exports=require("react/jsx-runtime")},2048:e=>{"use strict";e.exports=require("fs")},5315:e=>{"use strict";e.exports=require("path")},6162:e=>{"use strict";e.exports=require("stream")},1568:e=>{"use strict";e.exports=require("zlib")},3590:e=>{"use strict";e.exports=import("react-toastify")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[2899,6212,1664,7441],()=>s(519));module.exports=a})();