"use strict";(()=>{var e={};e.id=4315,e.ids=[4315],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},2667:(e,r,t)=>{t.r(r),t.d(r,{config:()=>g,default:()=>_,routeModule:()=>y});var o={};t.r(o),t.d(o,{default:()=>f,runScheduledAlertCheck:()=>h});var n=t(1802),a=t(7153),i=t(8781),s=t(7474);async function l(){let e=`alert-check-${Date.now()}`;try{console.log(`[${e}] Starting low stock alert check`);let{data:r,error:t}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory").select("id, name, sku, quantity_on_hand, min_stock_level, reorder_point, supplier_id").eq("is_active",!0);if(t)throw console.error(`[${e}] Error fetching inventory:`,t),t;if(!r||0===r.length)return console.log(`[${e}] No inventory items found`),{alertsCreated:0,notificationsSent:0};let o=0,n=0;for(let t of r){let r=Math.max(t.min_stock_level||0,t.reorder_point||0),a=t.quantity_on_hand||0,i=null;if(0===a?i="out_of_stock":a<=r&&(i="low_stock"),i){let{data:s}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory_alerts").select("id").eq("inventory_id",t.id).eq("alert_type",i).eq("is_active",!0).eq("is_resolved",!1).single();if(!s){let{data:s,error:l}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory_alerts").insert([{inventory_id:t.id,alert_type:i,threshold_value:r,current_value:a,is_active:!0,is_resolved:!1}]).select().single();if(l){console.error(`[${e}] Error creating alert for ${t.name}:`,l);continue}o++,console.log(`[${e}] Created ${i} alert for ${t.name} (${a}/${r})`);try{await c(t,i,a,r,e),n++}catch(r){console.error(`[${e}] Error sending notifications for ${t.name}:`,r)}}}}return console.log(`[${e}] Alert check completed:`,{itemsChecked:r.length,alertsCreated:o,notificationsSent:n}),{alertsCreated:o,notificationsSent:n}}catch(r){throw console.error(`[${e}] Error in checkLowStockAlerts:`,r),r}}async function c(e,r,t,o,n){try{let{data:a}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("system_settings").select("key, value").in("key",["inventory_alerts_enabled","inventory_alert_email","inventory_alert_sms"]),i={};if(a?.forEach(e=>{i[e.key]=e.value}),"true"!==i.inventory_alerts_enabled){console.log(`[${n}] Inventory alerts disabled in settings`);return}let s="out_of_stock"===r?"Out of Stock Alert":"Low Stock Alert",l=`${s}: ${e.name} ${e.sku?`(${e.sku})`:""} - Current stock: ${t}, Threshold: ${o}`,c=i.inventory_alert_email;if(c){let r={subject:`${s} - ${e.name}`,html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; font-size: 24px;">${s}</h1>
            </div>
            <div style="background: white; padding: 20px; border: 1px solid #e2e8f0; border-radius: 0 0 8px 8px;">
              <h2 style="color: #1e293b; margin-top: 0;">${e.name}</h2>
              ${e.sku?`<p><strong>SKU:</strong> ${e.sku}</p>`:""}
              <div style="background: #fee2e2; border: 1px solid #fca5a5; border-radius: 6px; padding: 15px; margin: 15px 0;">
                <p style="margin: 0; color: #991b1b;">
                  <strong>Current Stock:</strong> ${t} units<br>
                  <strong>Threshold:</strong> ${o} units
                </p>
              </div>
              <p>This item needs to be restocked to maintain adequate inventory levels.</p>
              <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e2e8f0;">
                <p style="color: #64748b; font-size: 14px; margin: 0;">
                  Ocean Soul Sparkles Inventory Management System<br>
                  Generated at ${new Date().toLocaleString("en-AU")}
                </p>
              </div>
            </div>
          </div>
        `,text:l};await Object(function(){var e=Error("Cannot find module '../notifications/email-service'");throw e.code="MODULE_NOT_FOUND",e}())(c,r),console.log(`[${n}] Email notification sent for ${e.name}`)}let d=i.inventory_alert_sms;d&&(await Object(function(){var e=Error("Cannot find module '../notifications/sms-service'");throw e.code="MODULE_NOT_FOUND",e}())(d,l),console.log(`[${n}] SMS notification sent for ${e.name}`))}catch(e){throw console.error(`[${n}] Error sending notifications:`,e),e}}async function d(){try{let{data:e,error:r}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory_alerts").select(`
        id,
        alert_type,
        threshold_value,
        current_value,
        created_at,
        inventory (
          id,
          name,
          sku,
          quantity_on_hand,
          min_stock_level
        )
      `).eq("is_active",!0).eq("is_resolved",!1).order("created_at",{ascending:!1});if(r)throw r;return e||[]}catch(e){throw console.error("Error fetching inventory alerts:",e),e}}async function u(e,r){try{let{data:t,error:o}=await Object(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory_alerts").update({is_resolved:!0,resolved_at:new Date().toISOString(),resolved_by:r}).eq("id",e).select().single();if(o)throw o;return t}catch(e){throw console.error("Error resolving inventory alert:",e),e}}async function f(e,r){let t=`alerts-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${t}] Inventory alerts API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let o=await (0,s.Wg)(e);if(!o.valid)return console.log(`[${t}] Authentication failed:`,o.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin","Artist","Braider"].includes(o.user.role))return console.log(`[${t}] Insufficient permissions:`,o.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await m(e,r,t);if("POST"===e.method){if(!["DEV","Admin"].includes(o.user.role))return r.status(403).json({error:"Insufficient permissions for this operation",requestId:t});return await v(e,r,t,o.user)}return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Inventory alerts API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function m(e,r,t){try{let e=(await d()).map(e=>({id:e.id,alertType:e.alert_type,thresholdValue:e.threshold_value,currentValue:e.current_value,createdAt:e.created_at,severity:"out_of_stock"===e.alert_type?"critical":"warning",item:{id:e.inventory?.id,name:e.inventory?.name,sku:e.inventory?.sku,currentStock:e.inventory?.quantity_on_hand,minStockLevel:e.inventory?.min_stock_level}})),o=e.filter(e=>"critical"===e.severity),n=e.filter(e=>"warning"===e.severity);return console.log(`[${t}] Alerts fetched successfully:`,{total:e.length,critical:o.length,warning:n.length}),r.status(200).json({alerts:e,summary:{total:e.length,critical:o.length,warning:n.length},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching alerts:`,e),e}}async function v(e,r,t,o){try{let{action:n,alertId:a}=e.body;if("check"===n){console.log(`[${t}] Manual alert check triggered by ${o.email}`);let e=await l();return console.log(`[${t}] Alert check completed:`,e),r.status(200).json({message:"Alert check completed",result:e,requestId:t})}if("resolve"===n){if(!a)return r.status(400).json({error:"Alert ID is required for resolve action",requestId:t});console.log(`[${t}] Resolving alert ${a} by ${o.email}`);let e=await u(a,o.id);return console.log(`[${t}] Alert resolved successfully:`,{alertId:e.id,resolvedBy:o.email}),r.status(200).json({message:"Alert resolved successfully",alert:e,requestId:t})}if("resolve_all"===n){console.log(`[${t}] Bulk resolving all alerts by ${o.email}`);let e=await d(),n=e.length;for(let r of e)await u(r.id,o.id);return console.log(`[${t}] Bulk resolve completed:`,{resolvedCount:n,resolvedBy:o.email}),r.status(200).json({message:`${n} alerts resolved successfully`,resolvedCount:n,requestId:t})}return r.status(400).json({error:"Invalid action. Supported actions: check, resolve, resolve_all",requestId:t})}catch(e){throw console.error(`[${t}] Error handling POST request:`,e),e}}async function h(){let e=`scheduled-${Date.now()}`;try{console.log(`[${e}] Running scheduled alert check`);let r=await l();return console.log(`[${e}] Scheduled alert check completed:`,r),r}catch(r){throw console.error(`[${e}] Error in scheduled alert check:`,r),r}}(function(){var e=Error("Cannot find module '../supabase-admin'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '../notifications/sms-service'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '../notifications/email-service'");throw e.code="MODULE_NOT_FOUND",e}();let _=(0,i.l)(o,"default"),g=(0,i.l)(o,"config"),y=new n.PagesAPIRouteModule({definition:{kind:a.x.PAGES_API,page:"/api/admin/inventory/alerts",pathname:"/api/admin/inventory/alerts",bundlePath:"",filename:""},userland:o})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[2805],()=>t(2667));module.exports=o})();