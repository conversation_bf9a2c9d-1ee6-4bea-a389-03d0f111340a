/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/settings";
exports.ids = ["pages/admin/settings"];
exports.modules = {

/***/ "./styles/admin/AdminHeader.module.css":
/*!*********************************************!*\
  !*** ./styles/admin/AdminHeader.module.css ***!
  \*********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"adminHeader\": \"AdminHeader_adminHeader__tAy8N\",\n\t\"headerLeft\": \"AdminHeader_headerLeft__FXjXr\",\n\t\"sidebarToggle\": \"AdminHeader_sidebarToggle__Vlukg\",\n\t\"hamburger\": \"AdminHeader_hamburger__3oPy_\",\n\t\"breadcrumb\": \"AdminHeader_breadcrumb__z_2w7\",\n\t\"breadcrumbLink\": \"AdminHeader_breadcrumbLink__iRTZW\",\n\t\"breadcrumbSeparator\": \"AdminHeader_breadcrumbSeparator__Q0xsW\",\n\t\"breadcrumbCurrent\": \"AdminHeader_breadcrumbCurrent__4QB_Y\",\n\t\"headerRight\": \"AdminHeader_headerRight__jgrCt\",\n\t\"quickActions\": \"AdminHeader_quickActions___NuOX\",\n\t\"quickAction\": \"AdminHeader_quickAction__XqmCI\",\n\t\"notifications\": \"AdminHeader_notifications__DWNcH\",\n\t\"notificationButton\": \"AdminHeader_notificationButton__hubpu\",\n\t\"notificationBadge\": \"AdminHeader_notificationBadge__spKqR\",\n\t\"notificationDropdown\": \"AdminHeader_notificationDropdown__mA8dq\",\n\t\"notificationHeader\": \"AdminHeader_notificationHeader__Ue15C\",\n\t\"markAllRead\": \"AdminHeader_markAllRead__UP_0Q\",\n\t\"notificationList\": \"AdminHeader_notificationList__JuL31\",\n\t\"notificationItem\": \"AdminHeader_notificationItem__ABEAH\",\n\t\"notificationIcon\": \"AdminHeader_notificationIcon__BSCLh\",\n\t\"notificationContent\": \"AdminHeader_notificationContent__tFkeh\",\n\t\"notificationTitle\": \"AdminHeader_notificationTitle__C5Il3\",\n\t\"notificationTime\": \"AdminHeader_notificationTime__DWutx\",\n\t\"notificationFooter\": \"AdminHeader_notificationFooter__T4khp\",\n\t\"userMenu\": \"AdminHeader_userMenu__YbO0w\",\n\t\"userButton\": \"AdminHeader_userButton__uP4qu\",\n\t\"userAvatar\": \"AdminHeader_userAvatar__QJdnj\",\n\t\"userInfo\": \"AdminHeader_userInfo__t2PHi\",\n\t\"userName\": \"AdminHeader_userName__4_RNy\",\n\t\"userRole\": \"AdminHeader_userRole__fQkGv\",\n\t\"dropdownArrow\": \"AdminHeader_dropdownArrow___vHwu\",\n\t\"userDropdown\": \"AdminHeader_userDropdown__NFy7A\",\n\t\"userDropdownHeader\": \"AdminHeader_userDropdownHeader__CYxvo\",\n\t\"userEmail\": \"AdminHeader_userEmail__nZCju\",\n\t\"userRoleBadge\": \"AdminHeader_userRoleBadge__W3Lbx\",\n\t\"userDropdownMenu\": \"AdminHeader_userDropdownMenu__7PJEX\",\n\t\"dropdownItem\": \"AdminHeader_dropdownItem__7zn2N\",\n\t\"dropdownIcon\": \"AdminHeader_dropdownIcon__ZZ3_U\",\n\t\"dropdownDivider\": \"AdminHeader_dropdownDivider__6AaxM\",\n\t\"logoutItem\": \"AdminHeader_logoutItem__R0CHw\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvYWRtaW4vQWRtaW5IZWFkZXIubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vc3R5bGVzL2FkbWluL0FkbWluSGVhZGVyLm1vZHVsZS5jc3M/NWNjOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJhZG1pbkhlYWRlclwiOiBcIkFkbWluSGVhZGVyX2FkbWluSGVhZGVyX190QXk4TlwiLFxuXHRcImhlYWRlckxlZnRcIjogXCJBZG1pbkhlYWRlcl9oZWFkZXJMZWZ0X19GWGpYclwiLFxuXHRcInNpZGViYXJUb2dnbGVcIjogXCJBZG1pbkhlYWRlcl9zaWRlYmFyVG9nZ2xlX19WbHVrZ1wiLFxuXHRcImhhbWJ1cmdlclwiOiBcIkFkbWluSGVhZGVyX2hhbWJ1cmdlcl9fM29QeV9cIixcblx0XCJicmVhZGNydW1iXCI6IFwiQWRtaW5IZWFkZXJfYnJlYWRjcnVtYl9fel8ydzdcIixcblx0XCJicmVhZGNydW1iTGlua1wiOiBcIkFkbWluSGVhZGVyX2JyZWFkY3J1bWJMaW5rX19pUlRaV1wiLFxuXHRcImJyZWFkY3J1bWJTZXBhcmF0b3JcIjogXCJBZG1pbkhlYWRlcl9icmVhZGNydW1iU2VwYXJhdG9yX19RMHhzV1wiLFxuXHRcImJyZWFkY3J1bWJDdXJyZW50XCI6IFwiQWRtaW5IZWFkZXJfYnJlYWRjcnVtYkN1cnJlbnRfXzRRQl9ZXCIsXG5cdFwiaGVhZGVyUmlnaHRcIjogXCJBZG1pbkhlYWRlcl9oZWFkZXJSaWdodF9famdyQ3RcIixcblx0XCJxdWlja0FjdGlvbnNcIjogXCJBZG1pbkhlYWRlcl9xdWlja0FjdGlvbnNfX19OdU9YXCIsXG5cdFwicXVpY2tBY3Rpb25cIjogXCJBZG1pbkhlYWRlcl9xdWlja0FjdGlvbl9fWHFtQ0lcIixcblx0XCJub3RpZmljYXRpb25zXCI6IFwiQWRtaW5IZWFkZXJfbm90aWZpY2F0aW9uc19fRFdOY0hcIixcblx0XCJub3RpZmljYXRpb25CdXR0b25cIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25CdXR0b25fX2h1YnB1XCIsXG5cdFwibm90aWZpY2F0aW9uQmFkZ2VcIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25CYWRnZV9fc3BLcVJcIixcblx0XCJub3RpZmljYXRpb25Ecm9wZG93blwiOiBcIkFkbWluSGVhZGVyX25vdGlmaWNhdGlvbkRyb3Bkb3duX19tQThkcVwiLFxuXHRcIm5vdGlmaWNhdGlvbkhlYWRlclwiOiBcIkFkbWluSGVhZGVyX25vdGlmaWNhdGlvbkhlYWRlcl9fVWUxNUNcIixcblx0XCJtYXJrQWxsUmVhZFwiOiBcIkFkbWluSGVhZGVyX21hcmtBbGxSZWFkX19VUF8wUVwiLFxuXHRcIm5vdGlmaWNhdGlvbkxpc3RcIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25MaXN0X19KdUwzMVwiLFxuXHRcIm5vdGlmaWNhdGlvbkl0ZW1cIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25JdGVtX19BQkVBSFwiLFxuXHRcIm5vdGlmaWNhdGlvbkljb25cIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25JY29uX19CU0NMaFwiLFxuXHRcIm5vdGlmaWNhdGlvbkNvbnRlbnRcIjogXCJBZG1pbkhlYWRlcl9ub3RpZmljYXRpb25Db250ZW50X190RmtlaFwiLFxuXHRcIm5vdGlmaWNhdGlvblRpdGxlXCI6IFwiQWRtaW5IZWFkZXJfbm90aWZpY2F0aW9uVGl0bGVfX0M1SWwzXCIsXG5cdFwibm90aWZpY2F0aW9uVGltZVwiOiBcIkFkbWluSGVhZGVyX25vdGlmaWNhdGlvblRpbWVfX0RXdXR4XCIsXG5cdFwibm90aWZpY2F0aW9uRm9vdGVyXCI6IFwiQWRtaW5IZWFkZXJfbm90aWZpY2F0aW9uRm9vdGVyX19UNGtocFwiLFxuXHRcInVzZXJNZW51XCI6IFwiQWRtaW5IZWFkZXJfdXNlck1lbnVfX1liTzB3XCIsXG5cdFwidXNlckJ1dHRvblwiOiBcIkFkbWluSGVhZGVyX3VzZXJCdXR0b25fX3VQNHF1XCIsXG5cdFwidXNlckF2YXRhclwiOiBcIkFkbWluSGVhZGVyX3VzZXJBdmF0YXJfX1FKZG5qXCIsXG5cdFwidXNlckluZm9cIjogXCJBZG1pbkhlYWRlcl91c2VySW5mb19fdDJQSGlcIixcblx0XCJ1c2VyTmFtZVwiOiBcIkFkbWluSGVhZGVyX3VzZXJOYW1lX180X1JOeVwiLFxuXHRcInVzZXJSb2xlXCI6IFwiQWRtaW5IZWFkZXJfdXNlclJvbGVfX2ZRa0d2XCIsXG5cdFwiZHJvcGRvd25BcnJvd1wiOiBcIkFkbWluSGVhZGVyX2Ryb3Bkb3duQXJyb3dfX192SHd1XCIsXG5cdFwidXNlckRyb3Bkb3duXCI6IFwiQWRtaW5IZWFkZXJfdXNlckRyb3Bkb3duX19ORnk3QVwiLFxuXHRcInVzZXJEcm9wZG93bkhlYWRlclwiOiBcIkFkbWluSGVhZGVyX3VzZXJEcm9wZG93bkhlYWRlcl9fQ1l4dm9cIixcblx0XCJ1c2VyRW1haWxcIjogXCJBZG1pbkhlYWRlcl91c2VyRW1haWxfX25aQ2p1XCIsXG5cdFwidXNlclJvbGVCYWRnZVwiOiBcIkFkbWluSGVhZGVyX3VzZXJSb2xlQmFkZ2VfX1czTGJ4XCIsXG5cdFwidXNlckRyb3Bkb3duTWVudVwiOiBcIkFkbWluSGVhZGVyX3VzZXJEcm9wZG93bk1lbnVfXzdQSkVYXCIsXG5cdFwiZHJvcGRvd25JdGVtXCI6IFwiQWRtaW5IZWFkZXJfZHJvcGRvd25JdGVtX183em4yTlwiLFxuXHRcImRyb3Bkb3duSWNvblwiOiBcIkFkbWluSGVhZGVyX2Ryb3Bkb3duSWNvbl9fWlozX1VcIixcblx0XCJkcm9wZG93bkRpdmlkZXJcIjogXCJBZG1pbkhlYWRlcl9kcm9wZG93bkRpdmlkZXJfXzZBYXhNXCIsXG5cdFwibG9nb3V0SXRlbVwiOiBcIkFkbWluSGVhZGVyX2xvZ291dEl0ZW1fX1IwQ0h3XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./styles/admin/AdminHeader.module.css\n");

/***/ }),

/***/ "./styles/admin/AdminLayout.module.css":
/*!*********************************************!*\
  !*** ./styles/admin/AdminLayout.module.css ***!
  \*********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"adminLayout\": \"AdminLayout_adminLayout__5Oi4c\",\n\t\"mainContent\": \"AdminLayout_mainContent__INtLu\",\n\t\"sidebarCollapsed\": \"AdminLayout_sidebarCollapsed__oAEhD\",\n\t\"pageContent\": \"AdminLayout_pageContent__aWMEk\",\n\t\"adminFooter\": \"AdminLayout_adminFooter__mTvA1\",\n\t\"footerContent\": \"AdminLayout_footerContent__z6du0\",\n\t\"footerLeft\": \"AdminLayout_footerLeft__gGY8P\",\n\t\"version\": \"AdminLayout_version__vpU9q\",\n\t\"footerRight\": \"AdminLayout_footerRight__kyodA\",\n\t\"footerLink\": \"AdminLayout_footerLink__jvWuv\",\n\t\"mobileOverlay\": \"AdminLayout_mobileOverlay__BNO2v\",\n\t\"securityBanner\": \"AdminLayout_securityBanner__KTGT5\",\n\t\"securityIcon\": \"AdminLayout_securityIcon__eZwIM\",\n\t\"loadingContainer\": \"AdminLayout_loadingContainer__Wbedv\",\n\t\"loadingSpinner\": \"AdminLayout_loadingSpinner__C8mvO\",\n\t\"spin\": \"AdminLayout_spin__DZv4U\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvYWRtaW4vQWRtaW5MYXlvdXQubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vc3R5bGVzL2FkbWluL0FkbWluTGF5b3V0Lm1vZHVsZS5jc3M/YmJjZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJhZG1pbkxheW91dFwiOiBcIkFkbWluTGF5b3V0X2FkbWluTGF5b3V0X181T2k0Y1wiLFxuXHRcIm1haW5Db250ZW50XCI6IFwiQWRtaW5MYXlvdXRfbWFpbkNvbnRlbnRfX0lOdEx1XCIsXG5cdFwic2lkZWJhckNvbGxhcHNlZFwiOiBcIkFkbWluTGF5b3V0X3NpZGViYXJDb2xsYXBzZWRfX29BRWhEXCIsXG5cdFwicGFnZUNvbnRlbnRcIjogXCJBZG1pbkxheW91dF9wYWdlQ29udGVudF9fYVdNRWtcIixcblx0XCJhZG1pbkZvb3RlclwiOiBcIkFkbWluTGF5b3V0X2FkbWluRm9vdGVyX19tVHZBMVwiLFxuXHRcImZvb3RlckNvbnRlbnRcIjogXCJBZG1pbkxheW91dF9mb290ZXJDb250ZW50X196NmR1MFwiLFxuXHRcImZvb3RlckxlZnRcIjogXCJBZG1pbkxheW91dF9mb290ZXJMZWZ0X19nR1k4UFwiLFxuXHRcInZlcnNpb25cIjogXCJBZG1pbkxheW91dF92ZXJzaW9uX192cFU5cVwiLFxuXHRcImZvb3RlclJpZ2h0XCI6IFwiQWRtaW5MYXlvdXRfZm9vdGVyUmlnaHRfX2t5b2RBXCIsXG5cdFwiZm9vdGVyTGlua1wiOiBcIkFkbWluTGF5b3V0X2Zvb3RlckxpbmtfX2p2V3V2XCIsXG5cdFwibW9iaWxlT3ZlcmxheVwiOiBcIkFkbWluTGF5b3V0X21vYmlsZU92ZXJsYXlfX0JOTzJ2XCIsXG5cdFwic2VjdXJpdHlCYW5uZXJcIjogXCJBZG1pbkxheW91dF9zZWN1cml0eUJhbm5lcl9fS1RHVDVcIixcblx0XCJzZWN1cml0eUljb25cIjogXCJBZG1pbkxheW91dF9zZWN1cml0eUljb25fX2Vad0lNXCIsXG5cdFwibG9hZGluZ0NvbnRhaW5lclwiOiBcIkFkbWluTGF5b3V0X2xvYWRpbmdDb250YWluZXJfX1diZWR2XCIsXG5cdFwibG9hZGluZ1NwaW5uZXJcIjogXCJBZG1pbkxheW91dF9sb2FkaW5nU3Bpbm5lcl9fQzhtdk9cIixcblx0XCJzcGluXCI6IFwiQWRtaW5MYXlvdXRfc3Bpbl9fRFp2NFVcIlxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./styles/admin/AdminLayout.module.css\n");

/***/ }),

/***/ "./styles/admin/AdminSidebar.module.css":
/*!**********************************************!*\
  !*** ./styles/admin/AdminSidebar.module.css ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"sidebar\": \"AdminSidebar_sidebar__qOEP2\",\n\t\"collapsed\": \"AdminSidebar_collapsed__mPopM\",\n\t\"mobile\": \"AdminSidebar_mobile__sXELg\",\n\t\"sidebarHeader\": \"AdminSidebar_sidebarHeader__h8NsD\",\n\t\"logo\": \"AdminSidebar_logo__MfKT2\",\n\t\"logoIcon\": \"AdminSidebar_logoIcon__ObH7O\",\n\t\"logoIconOnly\": \"AdminSidebar_logoIconOnly__AoqbB\",\n\t\"logoText\": \"AdminSidebar_logoText__6AwFU\",\n\t\"logoTitle\": \"AdminSidebar_logoTitle__rj3SO\",\n\t\"logoSubtitle\": \"AdminSidebar_logoSubtitle__ZlArc\",\n\t\"toggleButton\": \"AdminSidebar_toggleButton__93srV\",\n\t\"userInfo\": \"AdminSidebar_userInfo__0v9i_\",\n\t\"userAvatar\": \"AdminSidebar_userAvatar__Rg3G_\",\n\t\"userDetails\": \"AdminSidebar_userDetails__kA16n\",\n\t\"userName\": \"AdminSidebar_userName__2reke\",\n\t\"userRole\": \"AdminSidebar_userRole__Bo1eM\",\n\t\"navigation\": \"AdminSidebar_navigation__LpNEH\",\n\t\"menuList\": \"AdminSidebar_menuList__krOTx\",\n\t\"menuItem\": \"AdminSidebar_menuItem__A5Arm\",\n\t\"menuLink\": \"AdminSidebar_menuLink__ZSnZI\",\n\t\"active\": \"AdminSidebar_active__4G9nw\",\n\t\"menuIcon\": \"AdminSidebar_menuIcon__yJF_1\",\n\t\"menuLabel\": \"AdminSidebar_menuLabel__WEpLi\",\n\t\"expandButton\": \"AdminSidebar_expandButton__qS2q4\",\n\t\"submenu\": \"AdminSidebar_submenu__4dAAZ\",\n\t\"submenuItem\": \"AdminSidebar_submenuItem__WiecI\",\n\t\"submenuLink\": \"AdminSidebar_submenuLink__ZYwCJ\",\n\t\"submenuIcon\": \"AdminSidebar_submenuIcon__ThbKs\",\n\t\"submenuLabel\": \"AdminSidebar_submenuLabel__ocpuH\",\n\t\"sidebarFooter\": \"AdminSidebar_sidebarFooter__NML_U\",\n\t\"footerContent\": \"AdminSidebar_footerContent__qOiZI\",\n\t\"versionInfo\": \"AdminSidebar_versionInfo__bpisr\",\n\t\"version\": \"AdminSidebar_version__EyLxD\",\n\t\"environment\": \"AdminSidebar_environment__teF9S\",\n\t\"securityIndicator\": \"AdminSidebar_securityIndicator__S_6EA\",\n\t\"securityIcon\": \"AdminSidebar_securityIcon__GdGG2\",\n\t\"securityText\": \"AdminSidebar_securityText___evKe\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/AdminSidebar.module.css\n");

/***/ }),

/***/ "./styles/admin/Settings.module.css":
/*!******************************************!*\
  !*** ./styles/admin/Settings.module.css ***!
  \******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"settingsContainer\": \"Settings_settingsContainer__E9ikM\",\n\t\"header\": \"Settings_header__iprwI\",\n\t\"title\": \"Settings_title__UPQFQ\",\n\t\"headerActions\": \"Settings_headerActions__RuDIB\",\n\t\"resetBtn\": \"Settings_resetBtn__Ibi0t\",\n\t\"saveBtn\": \"Settings_saveBtn__8_bRU\",\n\t\"settingsContent\": \"Settings_settingsContent__nNeed\",\n\t\"tabNavigation\": \"Settings_tabNavigation__IKynR\",\n\t\"tabButton\": \"Settings_tabButton__jz8Cs\",\n\t\"active\": \"Settings_active__0_8Bo\",\n\t\"tabContent\": \"Settings_tabContent__cAICo\",\n\t\"settingsSection\": \"Settings_settingsSection__GC4In\",\n\t\"settingsGrid\": \"Settings_settingsGrid__wLtMu\",\n\t\"settingItem\": \"Settings_settingItem__KxWbr\",\n\t\"comingSoon\": \"Settings_comingSoon__Szu72\",\n\t\"loadingContainer\": \"Settings_loadingContainer__uPTPv\",\n\t\"loadingSpinner\": \"Settings_loadingSpinner__6_Tk_\",\n\t\"spin\": \"Settings_spin__3eCCm\",\n\t\"accessDenied\": \"Settings_accessDenied___aepr\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvYWRtaW4vU2V0dGluZ3MubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vc3R5bGVzL2FkbWluL1NldHRpbmdzLm1vZHVsZS5jc3M/YmRmZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJzZXR0aW5nc0NvbnRhaW5lclwiOiBcIlNldHRpbmdzX3NldHRpbmdzQ29udGFpbmVyX19FOWlrTVwiLFxuXHRcImhlYWRlclwiOiBcIlNldHRpbmdzX2hlYWRlcl9faXByd0lcIixcblx0XCJ0aXRsZVwiOiBcIlNldHRpbmdzX3RpdGxlX19VUFFGUVwiLFxuXHRcImhlYWRlckFjdGlvbnNcIjogXCJTZXR0aW5nc19oZWFkZXJBY3Rpb25zX19SdURJQlwiLFxuXHRcInJlc2V0QnRuXCI6IFwiU2V0dGluZ3NfcmVzZXRCdG5fX0liaTB0XCIsXG5cdFwic2F2ZUJ0blwiOiBcIlNldHRpbmdzX3NhdmVCdG5fXzhfYlJVXCIsXG5cdFwic2V0dGluZ3NDb250ZW50XCI6IFwiU2V0dGluZ3Nfc2V0dGluZ3NDb250ZW50X19uTmVlZFwiLFxuXHRcInRhYk5hdmlnYXRpb25cIjogXCJTZXR0aW5nc190YWJOYXZpZ2F0aW9uX19JS3luUlwiLFxuXHRcInRhYkJ1dHRvblwiOiBcIlNldHRpbmdzX3RhYkJ1dHRvbl9fano4Q3NcIixcblx0XCJhY3RpdmVcIjogXCJTZXR0aW5nc19hY3RpdmVfXzBfOEJvXCIsXG5cdFwidGFiQ29udGVudFwiOiBcIlNldHRpbmdzX3RhYkNvbnRlbnRfX2NBSUNvXCIsXG5cdFwic2V0dGluZ3NTZWN0aW9uXCI6IFwiU2V0dGluZ3Nfc2V0dGluZ3NTZWN0aW9uX19HQzRJblwiLFxuXHRcInNldHRpbmdzR3JpZFwiOiBcIlNldHRpbmdzX3NldHRpbmdzR3JpZF9fd0x0TXVcIixcblx0XCJzZXR0aW5nSXRlbVwiOiBcIlNldHRpbmdzX3NldHRpbmdJdGVtX19LeFdiclwiLFxuXHRcImNvbWluZ1Nvb25cIjogXCJTZXR0aW5nc19jb21pbmdTb29uX19TenU3MlwiLFxuXHRcImxvYWRpbmdDb250YWluZXJcIjogXCJTZXR0aW5nc19sb2FkaW5nQ29udGFpbmVyX191UFRQdlwiLFxuXHRcImxvYWRpbmdTcGlubmVyXCI6IFwiU2V0dGluZ3NfbG9hZGluZ1NwaW5uZXJfXzZfVGtfXCIsXG5cdFwic3BpblwiOiBcIlNldHRpbmdzX3NwaW5fXzNlQ0NtXCIsXG5cdFwiYWNjZXNzRGVuaWVkXCI6IFwiU2V0dGluZ3NfYWNjZXNzRGVuaWVkX19fYWVwclwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./styles/admin/Settings.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Csettings.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Csettings.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\admin\\settings.js */ \"./pages/admin/settings.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/admin/settings\",\n        pathname: \"/admin/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_admin_settings_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Csettings.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/admin/AdminHeader.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminHeader.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../styles/admin/AdminHeader.module.css */ \"./styles/admin/AdminHeader.module.css\");\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction AdminHeader({ user, onLogout, onToggleSidebar, sidebarCollapsed }) {\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const notificationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                setShowUserMenu(false);\n            }\n            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n                setShowNotifications(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"DEV\":\n                return \"#dc3545\";\n            case \"Admin\":\n                return \"#3788d8\";\n            case \"Artist\":\n                return \"#28a745\";\n            case \"Braider\":\n                return \"#fd7e14\";\n            default:\n                return \"#6c757d\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().adminHeader),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerLeft),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().sidebarToggle),\n                        onClick: onToggleSidebar,\n                        title: sidebarCollapsed ? \"Expand sidebar\" : \"Collapse sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().hamburger),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumb),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbLink),\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbSeparator),\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbCurrent),\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerRight),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/bookings/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"New Booking\",\n                                children: \"\\uD83D\\uDCC5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/customers/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"New Customer\",\n                                children: \"\\uD83D\\uDC64\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"Refresh\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notifications),\n                        ref: notificationsRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationButton),\n                                onClick: ()=>setShowNotifications(!showNotifications),\n                                title: \"Notifications\",\n                                children: [\n                                    \"\\uD83D\\uDD14\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationBadge),\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().markAllRead),\n                                                children: \"Mark all read\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationList),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCC5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"New booking request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"5 minutes ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"Payment received\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"1 hour ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"⚠️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"Low inventory alert\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"2 hours ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationFooter),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin/notifications\",\n                                            children: \"View all notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userMenu),\n                        ref: userMenuRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userButton),\n                                onClick: ()=>setShowUserMenu(!showUserMenu),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userAvatar),\n                                        children: [\n                                            user.firstName.charAt(0),\n                                            user.lastName.charAt(0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userInfo),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userName),\n                                                children: [\n                                                    user.firstName,\n                                                    \" \",\n                                                    user.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userRole),\n                                                style: {\n                                                    color: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownArrow),\n                                        children: showUserMenu ? \"▲\" : \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdownHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userEmail),\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userRoleBadge),\n                                                style: {\n                                                    backgroundColor: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/profile\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDC64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/security\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security & MFA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/preferences\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"⚙️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/help\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"❓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Help & Support\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem)} ${(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().logoutItem)}`,\n                                                onClick: onLogout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDEAA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/AdminHeader.tsx\n");

/***/ }),

/***/ "./components/admin/AdminLayout.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminLayout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _AdminSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AdminSidebar */ \"./components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _AdminHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AdminHeader */ \"./components/admin/AdminHeader.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../styles/admin/AdminLayout.module.css */ \"./styles/admin/AdminLayout.module.css\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_4__]);\nreact_toastify__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\nfunction AdminLayout({ children }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if mobile\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            if (window.innerWidth < 768) {\n                setSidebarCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Redirect to login if not authenticated\n        if (!loading && !user) {\n            router.push(\"/admin/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    const handleLogout = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (response.ok) {\n                localStorage.removeItem(\"admin-token\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Logged out successfully\");\n                router.push(\"/admin/login\");\n            } else {\n                throw new Error(\"Logout failed\");\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // Force logout even if API call fails\n            localStorage.removeItem(\"admin-token\");\n            router.push(\"/admin/login\");\n        }\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading admin portal...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().adminLayout),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                user: user,\n                collapsed: sidebarCollapsed,\n                onToggle: toggleSidebar,\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().mainContent)} ${sidebarCollapsed ? (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().sidebarCollapsed) : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        user: user,\n                        onLogout: handleLogout,\n                        onToggleSidebar: toggleSidebar,\n                        sidebarCollapsed: sidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().pageContent),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().adminFooter),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLeft),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xa9 2024 Ocean Soul Sparkles Admin Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().version),\n                                            children: \"v1.0.0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerRight),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/help\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Help\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/privacy\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Privacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/terms\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            isMobile && !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().mobileOverlay),\n                onClick: ()=>setSidebarCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().securityBanner),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().securityIcon),\n                        children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Secure Admin Portal - All actions are logged and monitored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/AdminLayout.tsx\n");

/***/ }),

/***/ "./components/admin/AdminSidebar.tsx":
/*!*******************************************!*\
  !*** ./components/admin/AdminSidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../styles/admin/AdminSidebar.module.css */ \"./styles/admin/AdminSidebar.module.css\");\n/* harmony import */ var _styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst MENU_ITEMS = [\n    {\n        id: \"dashboard\",\n        label: \"Dashboard\",\n        icon: \"\\uD83D\\uDCCA\",\n        href: \"/admin/dashboard\",\n        roles: [\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]\n    },\n    {\n        id: \"bookings\",\n        label: \"Bookings\",\n        icon: \"\\uD83D\\uDCC5\",\n        href: \"/admin/bookings\",\n        roles: [\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]\n    },\n    {\n        id: \"customers\",\n        label: \"Customers\",\n        icon: \"\\uD83D\\uDC65\",\n        href: \"/admin/customers\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"services\",\n        label: \"Services\",\n        icon: \"✨\",\n        href: \"/admin/services\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"products\",\n        label: \"Products\",\n        icon: \"\\uD83D\\uDECD️\",\n        href: \"/admin/products\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"staff\",\n        label: \"Staff Management\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\",\n        href: \"/admin/staff\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ],\n        children: [\n            {\n                id: \"staff-overview\",\n                label: \"Staff Overview\",\n                icon: \"\\uD83D\\uDC65\",\n                href: \"/admin/staff\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-onboarding\",\n                label: \"Onboarding\",\n                icon: \"\\uD83D\\uDCCB\",\n                href: \"/admin/staff/onboarding\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-training\",\n                label: \"Training\",\n                icon: \"\\uD83C\\uDF93\",\n                href: \"/admin/staff/training\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-performance\",\n                label: \"Performance\",\n                icon: \"\\uD83D\\uDCCA\",\n                href: \"/admin/staff/performance\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            }\n        ]\n    },\n    {\n        id: \"artists\",\n        label: \"Artists\",\n        icon: \"\\uD83C\\uDFA8\",\n        href: \"/admin/artists\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"tips\",\n        label: \"Tip Management\",\n        icon: \"\\uD83D\\uDCB0\",\n        href: \"/admin/tips\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"receipts\",\n        label: \"Receipts\",\n        icon: \"\\uD83E\\uDDFE\",\n        href: \"/admin/receipts\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"reports\",\n        label: \"Reports\",\n        icon: \"\\uD83D\\uDCC8\",\n        href: \"/admin/reports\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"communications\",\n        label: \"Communications\",\n        icon: \"\\uD83D\\uDCE7\",\n        href: \"/admin/communications\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ],\n        children: [\n            {\n                id: \"email-templates\",\n                label: \"Email Templates\",\n                icon: \"\\uD83D\\uDCDD\",\n                href: \"/admin/email-templates\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"sms-templates\",\n                label: \"SMS Templates\",\n                icon: \"\\uD83D\\uDCF1\",\n                href: \"/admin/sms-templates\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"communications-log\",\n                label: \"Communications Log\",\n                icon: \"\\uD83D\\uDCCB\",\n                href: \"/admin/communications\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"feedback\",\n                label: \"Customer Feedback\",\n                icon: \"⭐\",\n                href: \"/admin/feedback\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            }\n        ]\n    },\n    {\n        id: \"notifications\",\n        label: \"Notifications\",\n        icon: \"\\uD83D\\uDD14\",\n        href: \"/admin/notifications\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\",\n        icon: \"⚙️\",\n        href: \"/admin/settings\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    }\n];\nfunction AdminSidebar({ user, collapsed, onToggle, isMobile }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const hasAccess = (roles)=>{\n        return roles.includes(user.role);\n    };\n    const isActive = (href)=>{\n        return router.pathname === href || router.pathname.startsWith(href + \"/\");\n    };\n    const filteredMenuItems = MENU_ITEMS.filter((item)=>hasAccess(item.roles));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebar)} ${collapsed ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().collapsed) : \"\"} ${isMobile ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobile) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebarHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logo),\n                        children: [\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoIcon),\n                                        children: \"\\uD83C\\uDF0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoText),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoTitle),\n                                                children: \"Ocean Soul\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoSubtitle),\n                                                children: \"Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoIconOnly),\n                                children: \"\\uD83C\\uDF0A\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().toggleButton),\n                        onClick: onToggle,\n                        title: collapsed ? \"Expand sidebar\" : \"Collapse sidebar\",\n                        children: collapsed ? \"→\" : \"←\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userInfo),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userAvatar),\n                        children: [\n                            user.firstName.charAt(0),\n                            user.lastName.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userDetails),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userName),\n                                children: [\n                                    user.firstName,\n                                    \" \",\n                                    user.lastName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userRole),\n                                children: user.role\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().navigation),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuList),\n                    children: filteredMenuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuItem),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLink)} ${isActive(item.href) ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : \"\"}`,\n                                    title: collapsed ? item.label : undefined,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuIcon),\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLabel),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 19\n                                        }, this),\n                                        !collapsed && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().expandButton),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                toggleExpanded(item.id);\n                                            },\n                                            children: expandedItems.includes(item.id) ? \"▼\" : \"▶\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                !collapsed && item.children && expandedItems.includes(item.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenu),\n                                    children: item.children.filter((child)=>hasAccess(child.roles)).map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuItem),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: child.href,\n                                                className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuLink)} ${isActive(child.href) ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuIcon),\n                                                        children: child.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuLabel),\n                                                        children: child.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, child.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebarFooter),\n                children: [\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().footerContent),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().versionInfo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().version),\n                                    children: \"v1.0.0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().environment),\n                                    children:  true ? \"DEV\" : 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityIndicator),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityIcon),\n                                children: \"\\uD83D\\uDD12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityText),\n                                children: \"Secure Portal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/AdminSidebar.tsx\n");

/***/ }),

/***/ "./hooks/useAuth.ts":
/*!**************************!*\
  !*** ./hooks/useAuth.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useAuth() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        loading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        try {\n            const token = localStorage.getItem(\"admin-token\");\n            if (!token) {\n                setAuthState({\n                    user: null,\n                    loading: false,\n                    error: null\n                });\n                return;\n            }\n            const response = await fetch(\"/api/auth/verify\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`\n                }\n            });\n            if (!response.ok) {\n                // Token is invalid\n                localStorage.removeItem(\"admin-token\");\n                setAuthState({\n                    user: null,\n                    loading: false,\n                    error: \"Session expired\"\n                });\n                return;\n            }\n            const data = await response.json();\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n        } catch (error) {\n            console.error(\"Auth check error:\", error);\n            localStorage.removeItem(\"admin-token\");\n            setAuthState({\n                user: null,\n                loading: false,\n                error: \"Authentication failed\"\n            });\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Login failed\");\n            }\n            if (data.requiresMFA) {\n                return {\n                    requiresMFA: true,\n                    user: data.user\n                };\n            }\n            localStorage.setItem(\"admin-token\", data.token);\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n            return {\n                success: true,\n                user: data.user\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    };\n    const verifyMFA = async (userId, mfaCode)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            const response = await fetch(\"/api/auth/mfa-verify\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId,\n                    mfaCode\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"MFA verification failed\");\n            }\n            localStorage.setItem(\"admin-token\", data.token);\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n            return {\n                success: true,\n                user: data.user\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"MFA verification failed\";\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"admin-token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"admin-token\");\n            setAuthState({\n                user: null,\n                loading: false,\n                error: null\n            });\n            router.push(\"/admin/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                user: prev.user ? {\n                    ...prev.user,\n                    ...updatedUser\n                } : null\n            }));\n    };\n    const hasPermission = (permission)=>{\n        if (!authState.user) return false;\n        // DEV role has all permissions\n        if (authState.user.role === \"DEV\") return true;\n        // Check specific permissions\n        return authState.user.permissions.includes(permission);\n    };\n    const hasRole = (roles)=>{\n        if (!authState.user) return false;\n        const roleArray = Array.isArray(roles) ? roles : [\n            roles\n        ];\n        return roleArray.includes(authState.user.role);\n    };\n    const isAdmin = ()=>{\n        return hasRole([\n            \"DEV\",\n            \"Admin\"\n        ]);\n    };\n    const isStaff = ()=>{\n        return hasRole([\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]);\n    };\n    return {\n        user: authState.user,\n        loading: authState.loading,\n        error: authState.error,\n        login,\n        verifyMFA,\n        logout,\n        updateUser,\n        hasPermission,\n        hasRole,\n        isAdmin,\n        isStaff,\n        checkAuth\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useAuth.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_3__]);\nreact_toastify__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction AdminApp({ Component, pageProps }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Disable right-click context menu in production\n        if (false) {}\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Clear console in production\n        if (false) {}\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"referrer\",\n                        content: \"strict-origin-when-cross-origin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow, noarchive, nosnippet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Ocean Soul Sparkles Admin Portal - Secure staff access only\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/admin/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/admin/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/admin/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/admin/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://js.squareup.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://api.onesignal.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Ocean Soul Sparkles Admin Portal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                newestOnTop: false,\n                closeOnClick: true,\n                rtl: false,\n                pauseOnFocusLoss: true,\n                draggable: true,\n                pauseOnHover: true,\n                theme: \"light\",\n                toastStyle: {\n                    fontFamily: \"inherit\",\n                    fontSize: \"14px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: \"0\",\n                    left: \"0\",\n                    right: \"0\",\n                    background: \"#ff6b6b\",\n                    color: \"white\",\n                    padding: \"4px\",\n                    textAlign: \"center\",\n                    fontSize: \"12px\",\n                    fontWeight: \"bold\",\n                    zIndex: 10000\n                },\n                children: \"\\uD83D\\uDEA7 DEVELOPMENT MODE - ADMIN PORTAL \\uD83D\\uDEA7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/admin/settings.js":
/*!*********************************!*\
  !*** ./pages/admin/settings.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"./components/admin/AdminLayout.tsx\");\n/* harmony import */ var _styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/admin/Settings.module.css */ \"./styles/admin/Settings.module.css\");\n/* harmony import */ var _styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__]);\n_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n/**\n * Settings Management Page\n * \n * This page provides a comprehensive interface for managing system settings,\n * business configuration, and admin preferences.\n */ function SettingsManagement() {\n    const { user, loading: authLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        general: {\n            businessName: \"Ocean Soul Sparkles\",\n            businessEmail: \"<EMAIL>\",\n            businessPhone: \"+61 XXX XXX XXX\",\n            businessAddress: \"Australia\",\n            timezone: \"Australia/Sydney\",\n            currency: \"AUD\"\n        },\n        booking: {\n            defaultBookingDuration: 60,\n            advanceBookingDays: 30,\n            cancellationHours: 24,\n            autoConfirmBookings: true,\n            requireDeposit: false,\n            depositPercentage: 20\n        },\n        payment: {\n            squareEnabled: true,\n            squareEnvironment: \"sandbox\",\n            cashEnabled: true,\n            cardEnabled: true,\n            allowPartialPayments: true,\n            autoProcessRefunds: false\n        },\n        notifications: {\n            // Global toggles\n            emailNotifications: true,\n            smsNotifications: false,\n            pushNotifications: false,\n            // Email notification types\n            emailBookingConfirmation: true,\n            emailBookingReminder: true,\n            emailBookingCancellation: true,\n            emailPaymentReceipt: true,\n            emailStaffNotification: true,\n            emailLowInventoryAlert: true,\n            emailPromotional: true,\n            // SMS notification types\n            smsBookingConfirmation: false,\n            smsBookingReminder: false,\n            smsBookingCancellation: false,\n            smsPaymentReceipt: false,\n            smsStaffNotification: false,\n            smsPromotional: false,\n            // Push notification types (for future)\n            pushBookingConfirmation: false,\n            pushBookingReminder: false,\n            pushBookingCancellation: false,\n            pushStaffNotification: false,\n            // General settings\n            bookingReminders: true,\n            reminderHours: 24,\n            adminNotifications: true,\n            customerNotifications: true,\n            // Fallback behavior\n            emailFallbackWhenSMSFails: true,\n            smsFallbackWhenEmailFails: false\n        },\n        security: {\n            sessionTimeout: 1800,\n            adminSessionTimeout: 1800,\n            maxLoginAttempts: 5,\n            lockoutDuration: 900,\n            requireMFA: false,\n            ipRestrictions: false\n        }\n    });\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadSettings();\n        }\n    }, [\n        user\n    ]);\n    const loadSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/admin/settings\", {\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setSettings(data.settings || settings);\n            } else {\n                console.log(\"Using default settings - API not available\");\n            }\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateSetting = (category, key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [category]: {\n                    ...prev[category],\n                    [key]: value\n                }\n            }));\n        setHasChanges(true);\n    };\n    const saveSettings = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch(\"/api/admin/settings\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                },\n                body: JSON.stringify({\n                    settings\n                })\n            });\n            if (response.ok) {\n                setHasChanges(false);\n                alert(\"Settings saved successfully!\");\n            } else {\n                alert(\"Failed to save settings. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            alert(\"Error saving settings. Please try again.\");\n        } finally{\n            setSaving(false);\n        }\n    };\n    const resetSettings = ()=>{\n        if (confirm(\"Are you sure you want to reset all settings to defaults?\")) {\n            loadSettings();\n            setHasChanges(false);\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().loadingContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().loadingSpinner)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading settings...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login via useAuth\n    }\n    // Check permissions\n    if (![\n        \"DEV\",\n        \"Admin\"\n    ].includes(user.role)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().accessDenied),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"You don't have permission to access system settings.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Settings | Ocean Soul Sparkles Admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Manage system settings and configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().header),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().title),\n                                children: \"Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().headerActions),\n                                children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: resetSettings,\n                                            className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().resetBtn),\n                                            disabled: saving,\n                                            children: \"Reset\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveSettings,\n                                            className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().saveBtn),\n                                            disabled: saving,\n                                            children: saving ? \"Saving...\" : \"Save Changes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsContent),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabNavigation),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabButton)} ${activeTab === \"general\" ? (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                        onClick: ()=>setActiveTab(\"general\"),\n                                        children: \"General\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabButton)} ${activeTab === \"booking\" ? (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                        onClick: ()=>setActiveTab(\"booking\"),\n                                        children: \"Booking\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabButton)} ${activeTab === \"payment\" ? (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                        onClick: ()=>setActiveTab(\"payment\"),\n                                        children: \"Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabButton)} ${activeTab === \"notifications\" ? (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                        onClick: ()=>setActiveTab(\"notifications\"),\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabButton)} ${activeTab === \"security\" ? (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                        onClick: ()=>setActiveTab(\"security\"),\n                                        children: \"Security\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().tabContent),\n                                children: [\n                                    activeTab === \"general\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"General Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Business Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: settings.general.businessName,\n                                                                onChange: (e)=>updateSetting(\"general\", \"businessName\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Business Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                value: settings.general.businessEmail,\n                                                                onChange: (e)=>updateSetting(\"general\", \"businessEmail\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Business Phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                value: settings.general.businessPhone,\n                                                                onChange: (e)=>updateSetting(\"general\", \"businessPhone\", e.target.value)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Business Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: settings.general.businessAddress,\n                                                                onChange: (e)=>updateSetting(\"general\", \"businessAddress\", e.target.value),\n                                                                rows: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Timezone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: settings.general.timezone,\n                                                                onChange: (e)=>updateSetting(\"general\", \"timezone\", e.target.value),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Australia/Sydney\",\n                                                                        children: \"Australia/Sydney\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Australia/Melbourne\",\n                                                                        children: \"Australia/Melbourne\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Australia/Brisbane\",\n                                                                        children: \"Australia/Brisbane\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Australia/Perth\",\n                                                                        children: \"Australia/Perth\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Currency\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: settings.general.currency,\n                                                                onChange: (e)=>updateSetting(\"general\", \"currency\", e.target.value),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"AUD\",\n                                                                        children: \"AUD - Australian Dollar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"USD\",\n                                                                        children: \"USD - US Dollar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"EUR\",\n                                                                        children: \"EUR - Euro\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"booking\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Booking Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Default Booking Duration (minutes)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: settings.booking.defaultBookingDuration,\n                                                                onChange: (e)=>updateSetting(\"booking\", \"defaultBookingDuration\", parseInt(e.target.value)),\n                                                                min: \"15\",\n                                                                max: \"480\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Advance Booking Days\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: settings.booking.advanceBookingDays,\n                                                                onChange: (e)=>updateSetting(\"booking\", \"advanceBookingDays\", parseInt(e.target.value)),\n                                                                min: \"1\",\n                                                                max: \"365\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Cancellation Notice (hours)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: settings.booking.cancellationHours,\n                                                                onChange: (e)=>updateSetting(\"booking\", \"cancellationHours\", parseInt(e.target.value)),\n                                                                min: \"1\",\n                                                                max: \"168\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: settings.booking.autoConfirmBookings,\n                                                                    onChange: (e)=>updateSetting(\"booking\", \"autoConfirmBookings\", e.target.checked)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Auto-confirm bookings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: settings.booking.requireDeposit,\n                                                                    onChange: (e)=>updateSetting(\"booking\", \"requireDeposit\", e.target.checked)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Require deposit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    settings.booking.requireDeposit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                children: \"Deposit Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: settings.booking.depositPercentage,\n                                                                onChange: (e)=>updateSetting(\"booking\", \"depositPercentage\", parseInt(e.target.value)),\n                                                                min: \"5\",\n                                                                max: \"100\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"payment\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Payment Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().comingSoon),\n                                                children: \"Payment settings configuration coming soon...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"notifications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGroup),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"Communication Channels\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.emailNotifications,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"emailNotifications\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Enable Email Notifications\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Master toggle for all email communications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.smsNotifications,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"smsNotifications\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 421,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Enable SMS Notifications\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Master toggle for all SMS communications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.pushNotifications,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"pushNotifications\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Enable Push Notifications\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Master toggle for push notifications (future feature)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGroup),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"Email Notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailBookingConfirmation,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailBookingConfirmation\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Confirmations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailBookingReminder,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailBookingReminder\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Reminders\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailBookingCancellation,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailBookingCancellation\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Cancellations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailPaymentReceipt,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailPaymentReceipt\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Payment Receipts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailStaffNotification,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailStaffNotification\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Staff Notifications\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.emailPromotional,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"emailPromotional\", e.target.checked),\n                                                                            disabled: !settings.notifications.emailNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Promotional Emails\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGroup),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"SMS Notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsBookingConfirmation,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsBookingConfirmation\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 523,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Confirmations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsBookingReminder,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsBookingReminder\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 534,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Reminders\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsBookingCancellation,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsBookingCancellation\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Booking Cancellations\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsPaymentReceipt,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsPaymentReceipt\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Payment Receipts\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsStaffNotification,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsStaffNotification\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Staff Notifications\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: settings.notifications.smsPromotional,\n                                                                            onChange: (e)=>updateSetting(\"notifications\", \"smsPromotional\", e.target.checked),\n                                                                            disabled: !settings.notifications.smsNotifications\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Promotional SMS\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 518,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGroup),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"General Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"Reminder Hours Before Appointment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"number\",\n                                                                        value: settings.notifications.reminderHours,\n                                                                        onChange: (e)=>updateSetting(\"notifications\", \"reminderHours\", parseInt(e.target.value)),\n                                                                        min: \"1\",\n                                                                        max: \"168\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"How many hours before appointment to send reminders\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.adminNotifications,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"adminNotifications\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 607,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Admin Notifications\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Receive notifications for admin events\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 614,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.customerNotifications,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"customerNotifications\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 618,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Customer Notifications\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Send notifications to customers\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 591,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGroup),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"Fallback Behavior\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsGrid),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.emailFallbackWhenSMSFails,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"emailFallbackWhenSMSFails\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Send Email When SMS Fails\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Automatically send email if SMS delivery fails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingItem),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: settings.notifications.smsFallbackWhenEmailFails,\n                                                                                onChange: (e)=>updateSetting(\"notifications\", \"smsFallbackWhenEmailFails\", e.target.checked)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Send SMS When Email Fails\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                        children: \"Automatically send SMS if email delivery fails\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                                lineNumber: 645,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 631,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().settingsSection),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_styles_admin_Settings_module_css__WEBPACK_IMPORTED_MODULE_5___default().comingSoon),\n                                                children: \"Security settings configuration coming soon...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\settings.js\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/settings.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "react-toastify":
/*!*********************************!*\
  !*** external "react-toastify" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-toastify"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Fsettings&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Csettings.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();