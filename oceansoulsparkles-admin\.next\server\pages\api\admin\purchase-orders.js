"use strict";(()=>{var e={};e.id=5249,e.ids=[5249],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},5875:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>c,routeModule:()=>p});var a={};t.r(a),t.d(a,{default:()=>u});var o=t(1802),n=t(7153),s=t(8781),i=t(7474);async function u(e,r){let t=`purchase-orders-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${t}] Purchase Orders API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let a=await (0,i.Wg)(e);if(!a.valid)return console.log(`[${t}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${t}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await d(e,r,t);if("POST"===e.method)return await l(e,r,t,a.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Purchase Orders API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function d(e,r,t){try{let{search:a="",status:o="all",supplier:n="all",page:s=1,limit:i=50,sortBy:u="order_date",sortOrder:d="desc"}=e.query,l=Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select(`
        *,
        suppliers (
          id,
          name,
          contact_person,
          email,
          phone
        ),
        admin_users!purchase_orders_created_by_fkey (
          id,
          first_name,
          last_name,
          email
        )
      `,{count:"exact"});a&&(l=l.or(`po_number.ilike.%${a}%,notes.ilike.%${a}%`)),"all"!==o&&(l=l.eq("status",o)),"all"!==n&&(l=l.eq("supplier_id",n));let c=["po_number","order_date","expected_delivery_date","total_amount","status"].includes(u)?u:"order_date";l=l.order(c,{ascending:"asc"===d});let m=Math.max(1,parseInt(s)),p=Math.min(100,Math.max(1,parseInt(i))),f=(m-1)*p;l=l.range(f,f+p-1);let{data:h,error:_,count:g}=await l;if(_)throw console.error(`[${t}] Database error:`,_),_;let b=Math.ceil(g/p);return console.log(`[${t}] Purchase orders fetched successfully:`,{count:h?.length||0,total:g,page:m,totalPages:b}),r.status(200).json({purchaseOrders:h||[],pagination:{page:m,limit:p,total:g,totalPages:b,hasNextPage:m<b,hasPrevPage:m>1},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching purchase orders:`,e),e}}async function l(e,r,t,a){try{let{supplierId:o,orderDate:n,expectedDeliveryDate:s,notes:i,items:u=[]}=e.body;if(!o)return r.status(400).json({error:"Validation failed",message:"Supplier ID is required",requestId:t});if(!u||0===u.length)return r.status(400).json({error:"Validation failed",message:"At least one item is required",requestId:t});let{data:d,error:l}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("id, name, is_active").eq("id",o).single();if(l||!d)return r.status(404).json({error:"Supplier not found",requestId:t});if(!d.is_active)return r.status(400).json({error:"Validation failed",message:"Cannot create purchase order for inactive supplier",requestId:t});let c=0,m=[];for(let e of u){if(!e.inventoryId||!e.quantity||!e.unitCost)return r.status(400).json({error:"Validation failed",message:"All items must have inventory ID, quantity, and unit cost",requestId:t});let{data:a,error:o}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory").select("id, name, is_active").eq("id",e.inventoryId).single();if(o||!a)return r.status(404).json({error:"Inventory item not found",message:`Inventory item ${e.inventoryId} not found`,requestId:t});let n=parseInt(e.quantity),s=parseFloat(e.unitCost),i=n*s;m.push({inventory_id:e.inventoryId,product_name:a.name,quantity:n,unit_cost:s,total_cost:i}),c+=i}let p=.1*c,f=c+p,{data:h,error:_}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).rpc("generate_po_number");if(_)throw console.error(`[${t}] Error generating PO number:`,_),_;let g={po_number:h,supplier_id:o,status:"draft",order_date:n||new Date().toISOString().split("T")[0],expected_delivery_date:s||null,subtotal:c,tax_amount:p,total_amount:f,notes:i?.trim()||null,created_by:a.id,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:b,error:O}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").insert([g]).select().single();if(O)throw console.error(`[${t}] Database error creating purchase order:`,O),O;let v=m.map(e=>({...e,purchase_order_id:b.id})),{data:y,error:w}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_order_items").insert(v).select();if(w)throw console.error(`[${t}] Database error creating purchase order items:`,w),await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").delete().eq("id",b.id),w;return console.log(`[${t}] Purchase order created successfully:`,{id:b.id,poNumber:b.po_number,supplier:d.name,totalAmount:b.total_amount,itemCount:y.length,createdBy:a.email}),r.status(201).json({purchaseOrder:{...b,items:y,supplier:d},message:"Purchase order created successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error creating purchase order:`,e),e}}!function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}();let c=(0,s.l)(a,"default"),m=(0,s.l)(a,"config"),p=new o.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/admin/purchase-orders",pathname:"/api/admin/purchase-orders",bundlePath:"",filename:""},userland:a})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[2805],()=>t(5875));module.exports=a})();