exports.id=7441,exports.ids=[7441],exports.modules={4830:e=>{e.exports={adminHeader:"AdminHeader_adminHeader__tAy8N",headerLeft:"AdminHeader_headerLeft__FXjXr",sidebarToggle:"AdminHeader_sidebarToggle__Vlukg",hamburger:"AdminHeader_hamburger__3oPy_",breadcrumb:"AdminHeader_breadcrumb__z_2w7",breadcrumbLink:"AdminHeader_breadcrumbLink__iRTZW",breadcrumbSeparator:"AdminHeader_breadcrumbSeparator__Q0xsW",breadcrumbCurrent:"AdminHeader_breadcrumbCurrent__4QB_Y",headerRight:"AdminHeader_headerRight__jgrCt",quickActions:"AdminHeader_quickActions___NuOX",quickAction:"AdminHeader_quickAction__XqmCI",notifications:"AdminHeader_notifications__DWNcH",notificationButton:"AdminHeader_notificationButton__hubpu",notificationBadge:"AdminHeader_notificationBadge__spKqR",notificationDropdown:"AdminHeader_notificationDropdown__mA8dq",notificationHeader:"AdminHeader_notificationHeader__Ue15C",markAllRead:"AdminHeader_markAllRead__UP_0Q",notificationList:"AdminHeader_notificationList__JuL31",notificationItem:"AdminHeader_notificationItem__ABEAH",notificationIcon:"AdminHeader_notificationIcon__BSCLh",notificationContent:"AdminHeader_notificationContent__tFkeh",notificationTitle:"AdminHeader_notificationTitle__C5Il3",notificationTime:"AdminHeader_notificationTime__DWutx",notificationFooter:"AdminHeader_notificationFooter__T4khp",userMenu:"AdminHeader_userMenu__YbO0w",userButton:"AdminHeader_userButton__uP4qu",userAvatar:"AdminHeader_userAvatar__QJdnj",userInfo:"AdminHeader_userInfo__t2PHi",userName:"AdminHeader_userName__4_RNy",userRole:"AdminHeader_userRole__fQkGv",dropdownArrow:"AdminHeader_dropdownArrow___vHwu",userDropdown:"AdminHeader_userDropdown__NFy7A",userDropdownHeader:"AdminHeader_userDropdownHeader__CYxvo",userEmail:"AdminHeader_userEmail__nZCju",userRoleBadge:"AdminHeader_userRoleBadge__W3Lbx",userDropdownMenu:"AdminHeader_userDropdownMenu__7PJEX",dropdownItem:"AdminHeader_dropdownItem__7zn2N",dropdownIcon:"AdminHeader_dropdownIcon__ZZ3_U",dropdownDivider:"AdminHeader_dropdownDivider__6AaxM",logoutItem:"AdminHeader_logoutItem__R0CHw"}},8172:e=>{e.exports={adminLayout:"AdminLayout_adminLayout__5Oi4c",mainContent:"AdminLayout_mainContent__INtLu",sidebarCollapsed:"AdminLayout_sidebarCollapsed__oAEhD",pageContent:"AdminLayout_pageContent__aWMEk",adminFooter:"AdminLayout_adminFooter__mTvA1",footerContent:"AdminLayout_footerContent__z6du0",footerLeft:"AdminLayout_footerLeft__gGY8P",version:"AdminLayout_version__vpU9q",footerRight:"AdminLayout_footerRight__kyodA",footerLink:"AdminLayout_footerLink__jvWuv",mobileOverlay:"AdminLayout_mobileOverlay__BNO2v",securityBanner:"AdminLayout_securityBanner__KTGT5",securityIcon:"AdminLayout_securityIcon__eZwIM",loadingContainer:"AdminLayout_loadingContainer__Wbedv",loadingSpinner:"AdminLayout_loadingSpinner__C8mvO",spin:"AdminLayout_spin__DZv4U"}},5523:e=>{e.exports={sidebar:"AdminSidebar_sidebar__qOEP2",collapsed:"AdminSidebar_collapsed__mPopM",mobile:"AdminSidebar_mobile__sXELg",sidebarHeader:"AdminSidebar_sidebarHeader__h8NsD",logo:"AdminSidebar_logo__MfKT2",logoIcon:"AdminSidebar_logoIcon__ObH7O",logoIconOnly:"AdminSidebar_logoIconOnly__AoqbB",logoText:"AdminSidebar_logoText__6AwFU",logoTitle:"AdminSidebar_logoTitle__rj3SO",logoSubtitle:"AdminSidebar_logoSubtitle__ZlArc",toggleButton:"AdminSidebar_toggleButton__93srV",userInfo:"AdminSidebar_userInfo__0v9i_",userAvatar:"AdminSidebar_userAvatar__Rg3G_",userDetails:"AdminSidebar_userDetails__kA16n",userName:"AdminSidebar_userName__2reke",userRole:"AdminSidebar_userRole__Bo1eM",navigation:"AdminSidebar_navigation__LpNEH",menuList:"AdminSidebar_menuList__krOTx",menuItem:"AdminSidebar_menuItem__A5Arm",menuLink:"AdminSidebar_menuLink__ZSnZI",active:"AdminSidebar_active__4G9nw",menuIcon:"AdminSidebar_menuIcon__yJF_1",menuLabel:"AdminSidebar_menuLabel__WEpLi",expandButton:"AdminSidebar_expandButton__qS2q4",submenu:"AdminSidebar_submenu__4dAAZ",submenuItem:"AdminSidebar_submenuItem__WiecI",submenuLink:"AdminSidebar_submenuLink__ZYwCJ",submenuIcon:"AdminSidebar_submenuIcon__ThbKs",submenuLabel:"AdminSidebar_submenuLabel__ocpuH",sidebarFooter:"AdminSidebar_sidebarFooter__NML_U",footerContent:"AdminSidebar_footerContent__qOiZI",versionInfo:"AdminSidebar_versionInfo__bpisr",version:"AdminSidebar_version__EyLxD",environment:"AdminSidebar_environment__teF9S",securityIndicator:"AdminSidebar_securityIndicator__S_6EA",securityIcon:"AdminSidebar_securityIcon__GdGG2",securityText:"AdminSidebar_securityText___evKe"}},1194:(e,i,n)=>{"use strict";n.d(i,{Z:()=>l});var a=n(997),r=n(6689),s=n(1664),o=n.n(s),t=n(4830),d=n.n(t);function l({user:e,onLogout:i,onToggleSidebar:n,sidebarCollapsed:s}){let[t,l]=(0,r.useState)(!1),[c,m]=(0,r.useState)(!1),u=(0,r.useRef)(null),_=(0,r.useRef)(null),h=e=>{switch(e){case"DEV":return"#dc3545";case"Admin":return"#3788d8";case"Artist":return"#28a745";case"Braider":return"#fd7e14";default:return"#6c757d"}};return(0,a.jsxs)("header",{className:d().adminHeader,children:[(0,a.jsxs)("div",{className:d().headerLeft,children:[a.jsx("button",{className:d().sidebarToggle,onClick:n,title:s?"Expand sidebar":"Collapse sidebar",children:(0,a.jsxs)("span",{className:d().hamburger,children:[a.jsx("span",{}),a.jsx("span",{}),a.jsx("span",{})]})}),(0,a.jsxs)("div",{className:d().breadcrumb,children:[a.jsx(o(),{href:"/admin/dashboard",className:d().breadcrumbLink,children:"Dashboard"}),a.jsx("span",{className:d().breadcrumbSeparator,children:"/"}),a.jsx("span",{className:d().breadcrumbCurrent,children:"Overview"})]})]}),(0,a.jsxs)("div",{className:d().headerRight,children:[(0,a.jsxs)("div",{className:d().quickActions,children:[a.jsx(o(),{href:"/admin/bookings/new",className:d().quickAction,title:"New Booking",children:"\uD83D\uDCC5"}),a.jsx(o(),{href:"/admin/customers/new",className:d().quickAction,title:"New Customer",children:"\uD83D\uDC64"}),a.jsx("button",{className:d().quickAction,title:"Refresh",children:"\uD83D\uDD04"})]}),(0,a.jsxs)("div",{className:d().notifications,ref:_,children:[(0,a.jsxs)("button",{className:d().notificationButton,onClick:()=>m(!c),title:"Notifications",children:["\uD83D\uDD14",a.jsx("span",{className:d().notificationBadge,children:"3"})]}),c&&(0,a.jsxs)("div",{className:d().notificationDropdown,children:[(0,a.jsxs)("div",{className:d().notificationHeader,children:[a.jsx("h3",{children:"Notifications"}),a.jsx("button",{className:d().markAllRead,children:"Mark all read"})]}),(0,a.jsxs)("div",{className:d().notificationList,children:[(0,a.jsxs)("div",{className:d().notificationItem,children:[a.jsx("div",{className:d().notificationIcon,children:"\uD83D\uDCC5"}),(0,a.jsxs)("div",{className:d().notificationContent,children:[a.jsx("div",{className:d().notificationTitle,children:"New booking request"}),a.jsx("div",{className:d().notificationTime,children:"5 minutes ago"})]})]}),(0,a.jsxs)("div",{className:d().notificationItem,children:[a.jsx("div",{className:d().notificationIcon,children:"\uD83D\uDCB0"}),(0,a.jsxs)("div",{className:d().notificationContent,children:[a.jsx("div",{className:d().notificationTitle,children:"Payment received"}),a.jsx("div",{className:d().notificationTime,children:"1 hour ago"})]})]}),(0,a.jsxs)("div",{className:d().notificationItem,children:[a.jsx("div",{className:d().notificationIcon,children:"⚠️"}),(0,a.jsxs)("div",{className:d().notificationContent,children:[a.jsx("div",{className:d().notificationTitle,children:"Low inventory alert"}),a.jsx("div",{className:d().notificationTime,children:"2 hours ago"})]})]})]}),a.jsx("div",{className:d().notificationFooter,children:a.jsx(o(),{href:"/admin/notifications",children:"View all notifications"})})]})]}),(0,a.jsxs)("div",{className:d().userMenu,ref:u,children:[(0,a.jsxs)("button",{className:d().userButton,onClick:()=>l(!t),children:[(0,a.jsxs)("div",{className:d().userAvatar,children:[e.firstName.charAt(0),e.lastName.charAt(0)]}),(0,a.jsxs)("div",{className:d().userInfo,children:[(0,a.jsxs)("div",{className:d().userName,children:[e.firstName," ",e.lastName]}),a.jsx("div",{className:d().userRole,style:{color:h(e.role)},children:e.role})]}),a.jsx("div",{className:d().dropdownArrow,children:t?"▲":"▼"})]}),t&&(0,a.jsxs)("div",{className:d().userDropdown,children:[(0,a.jsxs)("div",{className:d().userDropdownHeader,children:[a.jsx("div",{className:d().userEmail,children:e.email}),a.jsx("div",{className:d().userRoleBadge,style:{backgroundColor:h(e.role)},children:e.role})]}),(0,a.jsxs)("div",{className:d().userDropdownMenu,children:[(0,a.jsxs)(o(),{href:"/admin/profile",className:d().dropdownItem,children:[a.jsx("span",{className:d().dropdownIcon,children:"\uD83D\uDC64"}),"Profile Settings"]}),(0,a.jsxs)(o(),{href:"/admin/security",className:d().dropdownItem,children:[a.jsx("span",{className:d().dropdownIcon,children:"\uD83D\uDD12"}),"Security & MFA"]}),(0,a.jsxs)(o(),{href:"/admin/preferences",className:d().dropdownItem,children:[a.jsx("span",{className:d().dropdownIcon,children:"⚙️"}),"Preferences"]}),a.jsx("div",{className:d().dropdownDivider}),(0,a.jsxs)(o(),{href:"/admin/help",className:d().dropdownItem,children:[a.jsx("span",{className:d().dropdownIcon,children:"❓"}),"Help & Support"]}),a.jsx("div",{className:d().dropdownDivider}),(0,a.jsxs)("button",{className:`${d().dropdownItem} ${d().logoutItem}`,onClick:i,children:[a.jsx("span",{className:d().dropdownIcon,children:"\uD83D\uDEAA"}),"Sign Out"]})]})]})]})]})]})}},4845:(e,i,n)=>{"use strict";n.a(e,async(e,a)=>{try{n.d(i,{Z:()=>p});var r=n(997),s=n(6689),o=n(1163),t=n(1664),d=n.n(t),l=n(3590),c=n(4528),m=n(1194),u=n(8568),_=n(8172),h=n.n(_),f=e([l]);function p({children:e}){let i=(0,o.useRouter)(),{user:n,loading:a}=(0,u.a)(),[t,_]=(0,s.useState)(!1),[f,p]=(0,s.useState)(!1),A=async()=>{try{if((await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("admin-token")}`}})).ok)localStorage.removeItem("admin-token"),l.toast.success("Logged out successfully"),i.push("/admin/login");else throw Error("Logout failed")}catch(e){console.error("Logout error:",e),localStorage.removeItem("admin-token"),i.push("/admin/login")}},b=()=>{_(!t)};return a?(0,r.jsxs)("div",{className:h().loadingContainer,children:[r.jsx("div",{className:h().loadingSpinner}),r.jsx("p",{children:"Loading admin portal..."})]}):n?(0,r.jsxs)("div",{className:h().adminLayout,children:[r.jsx(c.Z,{user:n,collapsed:t,onToggle:b,isMobile:f}),(0,r.jsxs)("div",{className:`${h().mainContent} ${t?h().sidebarCollapsed:""}`,children:[r.jsx(m.Z,{user:n,onLogout:A,onToggleSidebar:b,sidebarCollapsed:t}),r.jsx("main",{className:h().pageContent,children:e}),r.jsx("footer",{className:h().adminFooter,children:(0,r.jsxs)("div",{className:h().footerContent,children:[(0,r.jsxs)("div",{className:h().footerLeft,children:[r.jsx("span",{children:"\xa9 2024 Ocean Soul Sparkles Admin Portal"}),r.jsx("span",{className:h().version,children:"v1.0.0"})]}),(0,r.jsxs)("div",{className:h().footerRight,children:[r.jsx(d(),{href:"/admin/help",className:h().footerLink,children:"Help"}),r.jsx(d(),{href:"/admin/privacy",className:h().footerLink,children:"Privacy"}),r.jsx(d(),{href:"/admin/terms",className:h().footerLink,children:"Terms"})]})]})})]}),f&&!t&&r.jsx("div",{className:h().mobileOverlay,onClick:()=>_(!0)}),(0,r.jsxs)("div",{className:h().securityBanner,children:[r.jsx("div",{className:h().securityIcon,children:"\uD83D\uDD12"}),r.jsx("span",{children:"Secure Admin Portal - All actions are logged and monitored"})]})]}):null}l=(f.then?(await f)():f)[0],a()}catch(e){a(e)}})},4528:(e,i,n)=>{"use strict";n.d(i,{Z:()=>m});var a=n(997),r=n(6689),s=n(1664),o=n.n(s),t=n(1163),d=n(5523),l=n.n(d);let c=[{id:"dashboard",label:"Dashboard",icon:"\uD83D\uDCCA",href:"/admin/dashboard",roles:["DEV","Admin","Artist","Braider"]},{id:"bookings",label:"Bookings",icon:"\uD83D\uDCC5",href:"/admin/bookings",roles:["DEV","Admin","Artist","Braider"]},{id:"customers",label:"Customers",icon:"\uD83D\uDC65",href:"/admin/customers",roles:["DEV","Admin"]},{id:"services",label:"Services",icon:"✨",href:"/admin/services",roles:["DEV","Admin"]},{id:"products",label:"Products",icon:"\uD83D\uDECD️",href:"/admin/products",roles:["DEV","Admin"]},{id:"suppliers",label:"Suppliers",icon:"\uD83D\uDCE6",href:"/admin/suppliers",roles:["DEV","Admin"]},{id:"purchase-orders",label:"Purchase Orders",icon:"\uD83D\uDCCB",href:"/admin/purchase-orders",roles:["DEV","Admin"]},{id:"staff",label:"Staff Management",icon:"\uD83D\uDC68‍\uD83D\uDCBC",href:"/admin/staff",roles:["DEV","Admin"],children:[{id:"staff-overview",label:"Staff Overview",icon:"\uD83D\uDC65",href:"/admin/staff",roles:["DEV","Admin"]},{id:"staff-onboarding",label:"Onboarding",icon:"\uD83D\uDCCB",href:"/admin/staff/onboarding",roles:["DEV","Admin"]},{id:"staff-training",label:"Training",icon:"\uD83C\uDF93",href:"/admin/staff/training",roles:["DEV","Admin"]},{id:"staff-performance",label:"Performance",icon:"\uD83D\uDCCA",href:"/admin/staff/performance",roles:["DEV","Admin"]}]},{id:"artists",label:"Artists",icon:"\uD83C\uDFA8",href:"/admin/artists",roles:["DEV","Admin"]},{id:"tips",label:"Tip Management",icon:"\uD83D\uDCB0",href:"/admin/tips",roles:["DEV","Admin"]},{id:"receipts",label:"Receipts",icon:"\uD83E\uDDFE",href:"/admin/receipts",roles:["DEV","Admin"]},{id:"reports",label:"Reports",icon:"\uD83D\uDCC8",href:"/admin/reports",roles:["DEV","Admin"]},{id:"communications",label:"Communications",icon:"\uD83D\uDCE7",href:"/admin/communications",roles:["DEV","Admin"],children:[{id:"email-templates",label:"Email Templates",icon:"\uD83D\uDCDD",href:"/admin/email-templates",roles:["DEV","Admin"]},{id:"sms-templates",label:"SMS Templates",icon:"\uD83D\uDCF1",href:"/admin/sms-templates",roles:["DEV","Admin"]},{id:"communications-log",label:"Communications Log",icon:"\uD83D\uDCCB",href:"/admin/communications",roles:["DEV","Admin"]},{id:"feedback",label:"Customer Feedback",icon:"⭐",href:"/admin/feedback",roles:["DEV","Admin"]}]},{id:"notifications",label:"Notifications",icon:"\uD83D\uDD14",href:"/admin/notifications",roles:["DEV","Admin"]},{id:"settings",label:"Settings",icon:"⚙️",href:"/admin/settings",roles:["DEV","Admin"]}];function m({user:e,collapsed:i,onToggle:n,isMobile:s}){let d=(0,t.useRouter)(),[m,u]=(0,r.useState)([]),_=e=>{u(i=>i.includes(e)?i.filter(i=>i!==e):[...i,e])},h=i=>i.includes(e.role),f=e=>d.pathname===e||d.pathname.startsWith(e+"/"),p=c.filter(e=>h(e.roles));return(0,a.jsxs)("aside",{className:`${l().sidebar} ${i?l().collapsed:""} ${s?l().mobile:""}`,children:[(0,a.jsxs)("div",{className:l().sidebarHeader,children:[(0,a.jsxs)("div",{className:l().logo,children:[!i&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:l().logoIcon,children:"\uD83C\uDF0A"}),(0,a.jsxs)("div",{className:l().logoText,children:[a.jsx("div",{className:l().logoTitle,children:"Ocean Soul"}),a.jsx("div",{className:l().logoSubtitle,children:"Admin"})]})]}),i&&a.jsx("div",{className:l().logoIconOnly,children:"\uD83C\uDF0A"})]}),!s&&a.jsx("button",{className:l().toggleButton,onClick:n,title:i?"Expand sidebar":"Collapse sidebar",children:i?"→":"←"})]}),(0,a.jsxs)("div",{className:l().userInfo,children:[(0,a.jsxs)("div",{className:l().userAvatar,children:[e.firstName.charAt(0),e.lastName.charAt(0)]}),!i&&(0,a.jsxs)("div",{className:l().userDetails,children:[(0,a.jsxs)("div",{className:l().userName,children:[e.firstName," ",e.lastName]}),a.jsx("div",{className:l().userRole,children:e.role})]})]}),a.jsx("nav",{className:l().navigation,children:a.jsx("ul",{className:l().menuList,children:p.map(e=>(0,a.jsxs)("li",{className:l().menuItem,children:[(0,a.jsxs)(o(),{href:e.href,className:`${l().menuLink} ${f(e.href)?l().active:""}`,title:i?e.label:void 0,children:[a.jsx("span",{className:l().menuIcon,children:e.icon}),!i&&a.jsx("span",{className:l().menuLabel,children:e.label}),!i&&e.children&&a.jsx("button",{className:l().expandButton,onClick:i=>{i.preventDefault(),_(e.id)},children:m.includes(e.id)?"▼":"▶"})]}),!i&&e.children&&m.includes(e.id)&&a.jsx("ul",{className:l().submenu,children:e.children.filter(e=>h(e.roles)).map(e=>a.jsx("li",{className:l().submenuItem,children:(0,a.jsxs)(o(),{href:e.href,className:`${l().submenuLink} ${f(e.href)?l().active:""}`,children:[a.jsx("span",{className:l().submenuIcon,children:e.icon}),a.jsx("span",{className:l().submenuLabel,children:e.label})]})},e.id))})]},e.id))})}),(0,a.jsxs)("div",{className:l().sidebarFooter,children:[!i&&a.jsx("div",{className:l().footerContent,children:(0,a.jsxs)("div",{className:l().versionInfo,children:[a.jsx("div",{className:l().version,children:"v1.0.0"}),a.jsx("div",{className:l().environment,children:"PROD"})]})}),(0,a.jsxs)("div",{className:l().securityIndicator,children:[a.jsx("div",{className:l().securityIcon,children:"\uD83D\uDD12"}),!i&&a.jsx("div",{className:l().securityText,children:"Secure Portal"})]})]})]})}},8568:(e,i,n)=>{"use strict";n.d(i,{a:()=>s});var a=n(6689),r=n(1163);function s(){let e=(0,r.useRouter)(),[i,n]=(0,a.useState)({user:null,loading:!0,error:null}),s=async()=>{try{let e=localStorage.getItem("admin-token");if(!e){n({user:null,loading:!1,error:null});return}let i=await fetch("/api/auth/verify",{headers:{Authorization:`Bearer ${e}`}});if(!i.ok){localStorage.removeItem("admin-token"),n({user:null,loading:!1,error:"Session expired"});return}let a=await i.json();n({user:a.user,loading:!1,error:null})}catch(e){console.error("Auth check error:",e),localStorage.removeItem("admin-token"),n({user:null,loading:!1,error:"Authentication failed"})}},o=async(e,i)=>{try{n(e=>({...e,loading:!0,error:null}));let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:i})}),r=await a.json();if(!a.ok)throw Error(r.error||"Login failed");if(r.requiresMFA)return{requiresMFA:!0,user:r.user};return localStorage.setItem("admin-token",r.token),n({user:r.user,loading:!1,error:null}),{success:!0,user:r.user}}catch(i){let e=i instanceof Error?i.message:"Login failed";throw n(i=>({...i,loading:!1,error:e})),i}},t=async(e,i)=>{try{n(e=>({...e,loading:!0,error:null}));let a=await fetch("/api/auth/mfa-verify",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,mfaCode:i})}),r=await a.json();if(!a.ok)throw Error(r.error||"MFA verification failed");return localStorage.setItem("admin-token",r.token),n({user:r.user,loading:!1,error:null}),{success:!0,user:r.user}}catch(i){let e=i instanceof Error?i.message:"MFA verification failed";throw n(i=>({...i,loading:!1,error:e})),i}},d=async()=>{try{let e=localStorage.getItem("admin-token");e&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${e}`}})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("admin-token"),n({user:null,loading:!1,error:null}),e.push("/admin/login")}},l=e=>!!i.user&&(Array.isArray(e)?e:[e]).includes(i.user.role);return{user:i.user,loading:i.loading,error:i.error,login:o,verifyMFA:t,logout:d,updateUser:e=>{n(i=>({...i,user:i.user?{...i.user,...e}:null}))},hasPermission:e=>!!i.user&&("DEV"===i.user.role||i.user.permissions.includes(e)),hasRole:l,isAdmin:()=>l(["DEV","Admin"]),isStaff:()=>l(["DEV","Admin","Artist","Braider"]),checkAuth:s}}},6814:(e,i,n)=>{"use strict";n.a(e,async(e,a)=>{try{n.r(i),n.d(i,{default:()=>l});var r=n(997),s=n(968),o=n.n(s);n(6689);var t=n(3590);n(8819),n(6764);var d=e([t]);function l({Component:e,pageProps:i}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o(),{children:[r.jsx("meta",{charSet:"utf-8"}),r.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),r.jsx("meta",{httpEquiv:"X-Content-Type-Options",content:"nosniff"}),r.jsx("meta",{httpEquiv:"X-XSS-Protection",content:"1; mode=block"}),r.jsx("meta",{name:"referrer",content:"strict-origin-when-cross-origin"}),r.jsx("meta",{name:"robots",content:"noindex, nofollow, noarchive, nosnippet"}),r.jsx("meta",{name:"googlebot",content:"noindex, nofollow"}),r.jsx("meta",{name:"description",content:"Ocean Soul Sparkles Admin Portal - Secure staff access only"}),r.jsx("link",{rel:"icon",href:"/admin/favicon.ico"}),r.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/admin/apple-touch-icon.png"}),r.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/admin/favicon-32x32.png"}),r.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/admin/favicon-16x16.png"}),r.jsx("meta",{name:"theme-color",content:"#3788d8"}),r.jsx("meta",{name:"msapplication-TileColor",content:"#3788d8"}),r.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),r.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),r.jsx("link",{rel:"preconnect",href:"https://ndlgbcsbidyhxbpqzgqp.supabase.co"}),r.jsx("link",{rel:"dns-prefetch",href:"https://js.squareup.com"}),r.jsx("link",{rel:"dns-prefetch",href:"https://api.onesignal.com"}),r.jsx("title",{children:"Ocean Soul Sparkles Admin Portal"})]}),r.jsx(e,{...i}),r.jsx(t.ToastContainer,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light",toastStyle:{fontFamily:"inherit",fontSize:"14px"}}),r.jsx("div",{style:{position:"fixed",bottom:"10px",right:"10px",background:"rgba(0, 0, 0, 0.1)",color:"rgba(0, 0, 0, 0.3)",padding:"4px 8px",borderRadius:"4px",fontSize:"10px",fontWeight:"bold",pointerEvents:"none",zIndex:9999,userSelect:"none"},children:"ADMIN PORTAL"}),!1]})}t=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},6764:()=>{}};