"use strict";(()=>{var e={};e.id=7725,e.ids=[7725],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},7666:(e,r,t)=>{t.r(r),t.d(r,{config:()=>c,default:()=>p,routeModule:()=>m});var a={};t.r(a),t.d(a,{default:()=>l});var s=t(1802),i=t(7153),n=t(8781),o=t(7474);async function l(e,r){let t=`suppliers-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{console.log(`[${t}] Suppliers API request:`,{method:e.method,query:e.query,userAgent:e.headers["user-agent"]});let a=await (0,o.Wg)(e);if(!a.valid)return console.log(`[${t}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${t}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await u(e,r,t);if("POST"===e.method)return await d(e,r,t,a.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Suppliers API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function u(e,r,t){try{let{search:a="",active:s="all",page:i=1,limit:n=50,sortBy:o="name",sortOrder:l="asc"}=e.query,u=Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("*",{count:"exact"});a&&(u=u.or(`name.ilike.%${a}%,contact_person.ilike.%${a}%,email.ilike.%${a}%`)),"all"!==s&&(u=u.eq("is_active","true"===s));let d=["name","contact_person","email","created_at","updated_at"].includes(o)?o:"name";u=u.order(d,{ascending:"desc"!==l});let p=Math.max(1,parseInt(i)),c=Math.min(100,Math.max(1,parseInt(n))),m=(p-1)*c;u=u.range(m,m+c-1);let{data:f,error:g,count:h}=await u;if(g)throw console.error(`[${t}] Database error:`,g),g;let w=Math.ceil(h/c);return console.log(`[${t}] Suppliers fetched successfully:`,{count:f?.length||0,total:h,page:p,totalPages:w}),r.status(200).json({suppliers:f||[],pagination:{page:p,limit:c,total:h,totalPages:w,hasNextPage:p<w,hasPrevPage:p>1},requestId:t})}catch(e){throw console.error(`[${t}] Error fetching suppliers:`,e),e}}async function d(e,r,t,a){try{let{name:s,contactPerson:i,email:n,phone:o,address:l,website:u,paymentTerms:d,leadTimeDays:p,minimumOrderAmount:c,notes:m}=e.body;if(!s||0===s.trim().length)return r.status(400).json({error:"Validation failed",message:"Supplier name is required",requestId:t});let{data:f}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("id").eq("name",s.trim()).single();if(f)return r.status(409).json({error:"Supplier already exists",message:"A supplier with this name already exists",requestId:t});if(n&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return r.status(400).json({error:"Validation failed",message:"Invalid email format",requestId:t});let g={name:s.trim(),contact_person:i?.trim()||null,email:n?.trim()||null,phone:o?.trim()||null,address:l?.trim()||null,website:u?.trim()||null,payment_terms:d?.trim()||"Net 30",lead_time_days:p?parseInt(p):7,minimum_order_amount:c?parseFloat(c):0,notes:m?.trim()||null,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:h,error:w}=await Object(function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").insert([g]).select().single();if(w)throw console.error(`[${t}] Database error creating supplier:`,w),w;return console.log(`[${t}] Supplier created successfully:`,{id:h.id,name:h.name,createdBy:a.id}),r.status(201).json({supplier:h,message:"Supplier created successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error creating supplier:`,e),e}}!function(){var e=Error("Cannot find module '../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}();let p=(0,n.l)(a,"default"),c=(0,n.l)(a,"config"),m=new s.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/admin/suppliers",pathname:"/api/admin/suppliers",bundlePath:"",filename:""},userland:a})}};var r=require("../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[2805],()=>t(7666));module.exports=a})();