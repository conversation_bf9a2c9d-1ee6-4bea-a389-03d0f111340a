"use strict";(()=>{var e={};e.id=1010,e.ids=[1010],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},546:(e,r,t)=>{t.r(r),t.d(r,{config:()=>m,default:()=>c,routeModule:()=>f});var s={};t.r(s),t.d(s,{default:()=>u});var a=t(1802),o=t(7153),i=t(8781),n=t(7474);async function u(e,r){let t=`supplier-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:s}=e.query;if(console.log(`[${t}] Individual supplier API request:`,{method:e.method,supplierId:s,userAgent:e.headers["user-agent"]}),!s||"string"!=typeof s)return r.status(400).json({error:"Invalid supplier ID",requestId:t});let a=await (0,n.Wg)(e);if(!a.valid)return console.log(`[${t}] Authentication failed:`,a.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(a.user.role))return console.log(`[${t}] Insufficient permissions:`,a.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});if("GET"===e.method)return await l(e,r,t,s);if("PUT"===e.method)return await d(e,r,t,s,a.user);if("DELETE"===e.method)return await p(e,r,t,s,a.user);return r.status(405).json({error:"Method not allowed",requestId:t})}catch(e){return console.error(`[${t}] Individual supplier API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function l(e,r,t,s){try{let{data:e,error:a}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("*").eq("id",s).single();if(a){if("PGRST116"===a.code)return r.status(404).json({error:"Supplier not found",requestId:t});throw a}let{data:o,error:i}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory").select("id, name, sku, quantity_on_hand, min_stock_level").eq("supplier_id",s).eq("is_active",!0).order("name");i&&console.warn(`[${t}] Error fetching inventory items:`,i);let{data:n,error:u}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select("id, po_number, status, order_date, total_amount").eq("supplier_id",s).order("order_date",{ascending:!1}).limit(10);return u&&console.warn(`[${t}] Error fetching purchase orders:`,u),console.log(`[${t}] Supplier fetched successfully:`,{id:e.id,name:e.name,inventoryItems:o?.length||0,purchaseOrders:n?.length||0}),r.status(200).json({supplier:e,inventoryItems:o||[],purchaseOrders:n||[],requestId:t})}catch(e){throw console.error(`[${t}] Error fetching supplier:`,e),e}}async function d(e,r,t,s,a){try{let{name:o,contactPerson:i,email:n,phone:u,address:l,website:d,paymentTerms:p,leadTimeDays:c,minimumOrderAmount:m,isActive:f,notes:h}=e.body;if(!o||0===o.trim().length)return r.status(400).json({error:"Validation failed",message:"Supplier name is required",requestId:t});let{data:g,error:_}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("id, name").eq("id",s).single();if(_){if("PGRST116"===_.code)return r.status(404).json({error:"Supplier not found",requestId:t});throw _}let{data:w}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("id").eq("name",o.trim()).neq("id",s).single();if(w)return r.status(409).json({error:"Supplier name already exists",message:"Another supplier with this name already exists",requestId:t});if(n&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n))return r.status(400).json({error:"Validation failed",message:"Invalid email format",requestId:t});let O={name:o.trim(),contact_person:i?.trim()||null,email:n?.trim()||null,phone:u?.trim()||null,address:l?.trim()||null,website:d?.trim()||null,payment_terms:p?.trim()||"Net 30",lead_time_days:c?parseInt(c):7,minimum_order_amount:m?parseFloat(m):0,is_active:void 0===f||!!f,notes:h?.trim()||null,updated_at:new Date().toISOString()},{data:b,error:v}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").update(O).eq("id",s).select().single();if(v)throw console.error(`[${t}] Database error updating supplier:`,v),v;return console.log(`[${t}] Supplier updated successfully:`,{id:b.id,name:b.name,updatedBy:a.id}),r.status(200).json({supplier:b,message:"Supplier updated successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error updating supplier:`,e),e}}async function p(e,r,t,s,a){try{let{data:e,error:o}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").select("id, name").eq("id",s).single();if(o){if("PGRST116"===o.code)return r.status(404).json({error:"Supplier not found",requestId:t});throw o}let{data:i,error:n}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select("id").eq("supplier_id",s).in("status",["draft","sent","confirmed"]).limit(1);if(n&&console.warn(`[${t}] Error checking purchase orders:`,n),i&&i.length>0)return r.status(409).json({error:"Cannot delete supplier",message:"Supplier has active purchase orders. Please complete or cancel them first.",requestId:t});let{data:u,error:l}=await Object(function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("suppliers").update({is_active:!1,updated_at:new Date().toISOString()}).eq("id",s).select().single();if(l)throw console.error(`[${t}] Database error deleting supplier:`,l),l;return console.log(`[${t}] Supplier deleted successfully:`,{id:u.id,name:u.name,deletedBy:a.id}),r.status(200).json({message:"Supplier deleted successfully",requestId:t})}catch(e){throw console.error(`[${t}] Error deleting supplier:`,e),e}}!function(){var e=Error("Cannot find module '../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}();let c=(0,i.l)(s,"default"),m=(0,i.l)(s,"config"),f=new a.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/admin/suppliers/[id]",pathname:"/api/admin/suppliers/[id]",bundlePath:"",filename:""},userland:s})}};var r=require("../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2805],()=>t(546));module.exports=s})();