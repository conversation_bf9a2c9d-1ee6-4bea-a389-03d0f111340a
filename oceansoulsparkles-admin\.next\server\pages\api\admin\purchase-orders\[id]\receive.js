"use strict";(()=>{var e={};e.id=1036,e.ids=[1036],e.modules={2885:e=>{e.exports=require("@supabase/supabase-js")},8432:e=>{e.exports=require("bcryptjs")},9344:e=>{e.exports=require("jsonwebtoken")},1287:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},9200:e=>{e.exports=require("speakeasy")},3478:(e,r,t)=>{t.r(r),t.d(r,{config:()=>l,default:()=>c,routeModule:()=>p});var a={};t.r(a),t.d(a,{default:()=>d});var o=t(1802),n=t(7153),i=t(8781),s=t(7474);async function d(e,r){let t=`po-receive-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let{id:a}=e.query;if(console.log(`[${t}] Purchase order receiving API request:`,{method:e.method,purchaseOrderId:a,userAgent:e.headers["user-agent"]}),"POST"!==e.method)return r.status(405).json({error:"Method not allowed",requestId:t});if(!a||"string"!=typeof a)return r.status(400).json({error:"Invalid purchase order ID",requestId:t});let o=await (0,s.Wg)(e);if(!o.valid)return console.log(`[${t}] Authentication failed:`,o.error),r.status(401).json({error:"Unauthorized",requestId:t});if(!["DEV","Admin"].includes(o.user.role))return console.log(`[${t}] Insufficient permissions:`,o.user.role),r.status(403).json({error:"Insufficient permissions",requestId:t});return await u(e,r,t,a,o.user)}catch(e){return console.error(`[${t}] Purchase order receiving API error:`,e),r.status(500).json({error:"Internal server error",message:e.message,requestId:t})}}async function u(e,r,t,a,o){try{let{receivedItems:n,actualDeliveryDate:i,notes:s}=e.body;if(!n||!Array.isArray(n)||0===n.length)return r.status(400).json({error:"Validation failed",message:"Received items array is required",requestId:t});let{data:d,error:u}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").select(`
        id,
        po_number,
        status,
        supplier_id,
        suppliers (name)
      `).eq("id",a).single();if(u){if("PGRST116"===u.code)return r.status(404).json({error:"Purchase order not found",requestId:t});throw u}if(!["confirmed","sent"].includes(d.status))return r.status(400).json({error:"Invalid purchase order status",message:"Purchase order must be confirmed or sent to receive items",requestId:t});let{data:c,error:l}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_order_items").select("*").eq("purchase_order_id",a);if(l)throw l;let p=new Map(c.map(e=>[e.id,e])),m=[],f=0;for(let e of n){let{itemId:a,receivedQuantity:o}=e;if(!a||void 0===o||o<0)return r.status(400).json({error:"Validation failed",message:"Each received item must have itemId and non-negative receivedQuantity",requestId:t});let n=p.get(a);if(!n)return r.status(404).json({error:"Purchase order item not found",message:`Item ${a} not found in purchase order`,requestId:t});let i=n.received_quantity+parseInt(o);if(i>n.quantity)return r.status(400).json({error:"Validation failed",message:`Cannot receive more than ordered quantity for item ${a}`,requestId:t});m.push({...n,receivedQuantity:parseInt(o),newReceivedQuantity:i}),f+=parseInt(o)*n.unit_cost}let h={updatedItems:[],updatedInventory:[],updatedPurchaseOrder:null};try{for(let e of m)if(e.receivedQuantity>0){let{data:r,error:a}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_order_items").update({received_quantity:e.newReceivedQuantity}).eq("id",e.id).select().single();if(a)throw a;if(h.updatedItems.push(r),e.inventory_id&&e.receivedQuantity>0){let{data:r,error:a}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("inventory").update({quantity_on_hand:Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).sql`quantity_on_hand + ${e.receivedQuantity}`,updated_at:new Date().toISOString()}).eq("id",e.inventory_id).select().single();a?console.warn(`[${t}] Error updating inventory for item ${e.id}:`,a):h.updatedInventory.push(r)}}let{data:e,error:n}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_order_items").select("quantity, received_quantity").eq("purchase_order_id",a);if(n)throw n;let u=e.every(e=>e.received_quantity>=e.quantity),c=u?"received":"confirmed",l={status:c,updated_at:new Date().toISOString()};i&&(l.actual_delivery_date=i),s&&(l.notes=s.trim());let{data:p,error:v}=await Object(function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}()).from("purchase_orders").update(l).eq("id",a).select().single();if(v)throw v;return h.updatedPurchaseOrder=p,console.log(`[${t}] Purchase order receiving completed successfully:`,{id:d.id,poNumber:d.po_number,supplier:d.suppliers?.name,itemsReceived:h.updatedItems.length,inventoryUpdated:h.updatedInventory.length,totalValue:f.toFixed(2),newStatus:c,fullyReceived:u,receivedBy:o.email}),r.status(200).json({message:u?"Purchase order fully received":"Items received successfully",purchaseOrder:h.updatedPurchaseOrder,receivedItems:h.updatedItems,updatedInventory:h.updatedInventory,summary:{itemsReceived:h.updatedItems.length,inventoryUpdated:h.updatedInventory.length,totalValue:f,fullyReceived:u},requestId:t})}catch(e){throw console.error(`[${t}] Error during receiving transaction:`,e),e}}catch(e){throw console.error(`[${t}] Error receiving purchase order:`,e),e}}!function(){var e=Error("Cannot find module '../../../../../lib/supabase-admin'");throw e.code="MODULE_NOT_FOUND",e}();let c=(0,i.l)(a,"default"),l=(0,i.l)(a,"config"),p=new o.PagesAPIRouteModule({definition:{kind:n.x.PAGES_API,page:"/api/admin/purchase-orders/[id]/receive",pathname:"/api/admin/purchase-orders/[id]/receive",bundlePath:"",filename:""},userland:a})}};var r=require("../../../../../webpack-api-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[2805],()=>t(3478));module.exports=a})();